#include "tasks_polling.h"

//*****************************common */
#include "converter.h"

//*****************************app */
#include "time_tasks.h"
#include "variable_talbe.h"


static PtrTimer    s_ptrTimer;
static PtrTimeTask s_ptrTimeTask;

static TaskComponents s_taskComponents1ms;
static TaskComponents s_taskComponents10ms;
static TaskComponents s_taskComponents50ms;
static TaskComponents s_taskComponents100ms;


static void Task_1ms_Process(void) {
}

static void Task_10ms_Process(void) {
#if !defined(A0_T5_XX) && !defined(A2_T5_XX) && !defined(A0_T5_UMOUSE) && !defined(A2_T5_UMOUSE)

    // uint8_t tmpBuffer[2] = {0};

    // if (g_ptrFlagCmd.cmdHandle0 == true) {
    //     g_ptrFlagCmd.cmdHandle0 = false;
    //     tmpBuffer[0]            = 0xFF;
    //     tmpBuffer[1]            = 0xFF;
    //     g_ptrCom.ComTransmitDmaData(tmpBuffer, 2);
    //     WaitTransmit();
    // } else if (g_ptrFlagCmd.cmdHandle1 == true) {
    //     g_ptrFlagCmd.cmdHandle1 = false;
    //     tmpBuffer[0]            = 0xFE;
    //     tmpBuffer[1]            = 0xFE;
    //     g_ptrCom.ComTransmitDmaData(tmpBuffer, 2);
    //     WaitTransmit();
    // } else if (g_ptrFlagCmd.cmdHandle2 == true) {
    //     g_ptrFlagCmd.cmdHandle2  = false;
    //     tmpBuffer[0]             = 0xFA;
    //     tmpBuffer[1]             = 0xFA;
    //     g_regSpeed.isSendStabled = true;
    //     g_ptrCom.ComTransmitDmaData(tmpBuffer, 2);
    //     WaitTransmit();
    // }
#endif
}


static void Task_50ms_Process(void) {
#if (defined A0_T5_XX) || (defined A2_T5_XX) || (defined A0_T5_UMOUSE) || (defined A2_T5_UMOUSE)
    static uint16_t interval_cnt = 0, interval_value = 1, interval_delta_cnt = 0;
    static uint32_t tim_val    = 0;
    static float    speed_duty = 0;
    bool            is_running = false;

    /*
    uint32_t sum_cur=0,tim_val=0;
    do{
        sum_cur = g_pid_speed.speed_sum_cur;
        tim_val = TIM_Base_Count_Get(TIM4);

    }while(sum_cur != g_pid_speed.speed_sum_cur);
    sum_cur += tim_val;

    if(sum_cur < g_pid_speed.speed_sum_last) {
        g_pid_speed.speed_feedback_us = sum_cur+0xFFFFFFFF+1-g_pid_speed.speed_sum_last;
    }
    else {
        g_pid_speed.speed_feedback_us = sum_cur - g_pid_speed.speed_sum_last;
    }
    g_pid_speed.speed_sum_last = sum_cur;
    */

    do {
        interval_cnt   = g_pid_speed.speed_interval_cnt;
        tim_val        = TIM_Base_Count_Get(TIM8);
        interval_value = g_regEncoder.calcEdgeCountAve;
    } while (g_pid_speed.speed_interval_cnt != interval_cnt);


    if (interval_cnt < g_pid_speed.speed_interval_cnt_last) {
        interval_delta_cnt = interval_cnt + 65536 - g_pid_speed.speed_interval_cnt_last;
    } else {
        interval_delta_cnt = interval_cnt - g_pid_speed.speed_interval_cnt_last;
    }

    g_pid_speed.speed_interval_cnt_last = interval_cnt;

    speed_duty                 = 24.0f * tim_val / interval_value;
    g_pid_speed.speed_feedback = (interval_delta_cnt * 24 + speed_duty - g_pid_speed.speed_duty_last);

    g_pid_speed.speed_duty_last = speed_duty;

    // pid control
    // printf("%d %.2f\r\n",(uint16_t)g_pid_speed.ctrl_out, g_pid_speed.speed_feedback);
    if (g_pid_speed.speed_feedback > 360 || g_pid_speed.speed_feedback < 0) {
        g_pid_speed.speed_feedback = 0;
    }
    if (g_param_combine.motor_mode.mode == kRunning || g_param_combine.motor_mode.mode == kTurnOn) {
        is_running = true;
    } else {
        is_running = false;
    }

    pid_calc_loop(&g_pid_speed, is_running);
    TIM_Compare4_Set(TIM1, (uint16_t)g_pid_speed.ctrl_out);

// printf("%.2f %.2f %.2f %d %d\r\n", g_pid_speed.speed_feedback*20.0f/360, g_pid_speed.err_last, g_pid_speed.integral, interval_value,
// (uint16_t)g_pid_speed.ctrl_out);
#endif
}

static void Task_100ms_Process(void) {
    /*enable adc sampling*/
    static uint8_t ddsCnt                     = 0;
    static uint8_t vi3202trigCnt              = 0;
    static uint8_t dds_trig_ng_wait_clear_cnt = 0;
    ddsCnt++;
    vi3202trigCnt++;
    
#if (defined A0_T5_XX) || (defined A2_T5_XX)
    if (g_regSpeed.isStalled == true) {
        if (getAbsolutionTime() - g_ptrDDSInfoCount.encoder_cnt > ENCODER_NG_COUNT && g_param_combine.motor_mode.mode != kTurnOff) {

            g_param_combine.motor_mode.mode = kTurnOff;
        }
    }
#endif
    if (vi3202trigCnt > VI4302_TRIG_DETECT_TIME) {
        vi3202trigCnt = 0;
        if (getAbsolutionTime() - g_ptrDDSInfoCount.sensorTrigCnt > VI4302_TRIG_DETECT_CYCLE) {  // sensor trig
            if (g_ptrLidarStatus.lidarMode == kTofScanMode || g_ptrLidarStatus.lidarMode == kTofCalibrationMode) {
                g_ptrSysParam.sensor_trig_ng_cnt++;
                dds_trig_ng_wait_clear_cnt = 0;
                g_ptrSysFlag.errorCode |= SET_16BIT(15);
            }
        } else {
            dds_trig_ng_wait_clear_cnt++;
            if (dds_trig_ng_wait_clear_cnt > 3) {
                g_ptrSysFlag.errorCode &= CLEAR_16BIT(15);
                dds_trig_ng_wait_clear_cnt = 0;
            }
        }
    }
    if (ddsCnt > DDS_DETECT_TIMES) {
        ddsCnt = 0;

        // dds detect
        // if (getAbsolutionTime() - g_ptrDDSInfoCount.sensorTrigCnt > DDS_DETECT_CYCLE) {  // sensor trig
        //     if (g_ptrLidarStatus.lidarMode == kTofScanMode || g_ptrLidarStatus.lidarMode == kTofCalibrationMode) {
        //         g_ptrSysParam.sensor_trig_ng_cnt++;
        //         g_ptrSysFlag.errorCode |= SET_16BIT(15);
        //     }
        // } else {
        //     g_ptrSysFlag.errorCode &= CLEAR_16BIT(15);
        // }

        if (getAbsolutionTime() - g_ptrDDSInfoCount.encoder_cnt > DDS_DETECT_CYCLE && g_regSpeed.isSpeedStabled == true &&
            g_regSpeed.isStalled == false) {  // encoder
            if (g_ptrLidarStatus.lidarMode == kTofScanMode || g_ptrLidarStatus.lidarMode == kTofCalibrationMode) {
                g_ptrSysFlag.errorCode |= SET_16BIT(11);
            }
        } else {
            g_ptrSysFlag.errorCode &= CLEAR_16BIT(11);
        }


        if (getAbsolutionTime() - g_ptrDDSInfoCount.laserCnt > DDS_DETECT_CYCLE && g_regSpeed.isSpeedStabled == true &&
            g_regSpeed.isStalled == false) {  // laser
            if (g_ptrLidarStatus.lidarMode == kTofScanMode || g_ptrLidarStatus.lidarMode == kTofCalibrationMode) {
                if ((g_ptrSysFlag.errorCode & 0x8000) != 0x8000) {
                    g_ptrSysFlag.errorCode |= SET_16BIT(13);
                }
            }
        } else {
            g_ptrSysFlag.errorCode &= CLEAR_16BIT(13);
        }

        if ((getAbsolutionTime() - g_ptrDDSInfoCount.tempCnt > 10000) &&
            ((fabs(g_ptrSysParam.currentTemperature) < 0.01 && fabs(g_ptrSysParam.temperatureLast) < 0.01) ||
             (fabs(g_ptrSysParam.currentTemperature - g_ptrSysParam.temperatureLast) < 20 && g_ptrSysParam.currentTemperature > 3560))) {
            if (g_ptrLidarStatus.lidarMode != kTofStopMode) {
                g_ptrSysFlag.errorCode |= SET_16BIT(2);
            }
        } else {
            g_ptrSysFlag.errorCode &= CLEAR_16BIT(2);
        }

        if (g_ptrSysFlag.errorCode != 0) {
            if (g_ptrSysFlag.errorCode != SET_16BIT(11)) {
                TOGGLE_LED1
            }

            g_ptrSysFlag.isDetectError = true;
        } else {
            g_ptrSysFlag.isDetectError = false;
        }
    }
}


/**
 * @brief
 *
 */
void TaskSystemInit(void) {
    // RegisterTaskCallBackFunc(s_ptrTimer, &g_taskComponents1ms,0,1,Task_1ms_Process);
    // 注册10ms定时任务回调函数
    RegisterTaskCallBackFunc(s_ptrTimer, &s_taskComponents10ms, 0, 10, Task_10ms_Process);
    // 注册50ms定时任务回调函数
    RegisterTaskCallBackFunc(s_ptrTimer, &s_taskComponents50ms, 0, 50, Task_50ms_Process);
    // 注册100ms定时任务回调函数
    RegisterTaskCallBackFunc(s_ptrTimer, &s_taskComponents100ms, 0, 100, Task_100ms_Process);
}