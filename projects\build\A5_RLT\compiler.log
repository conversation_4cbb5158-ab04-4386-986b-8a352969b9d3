>>> cc

".\..\utils\src\protocol.c", line 30: Warning:  #177-D: function "ProtocolDefaultHandler"  was declared but never referenced
  static ProtocolError ProtocolDefaultHandler(const ProtocolMessage *msg) {
                       ^
.\..\utils\src\protocol.c: 1 warning, 0 errors
".\app\src\communication.c", line 119: Error:  #20: identifier "kOk" is undefined
      if (fdbType == kOk) {
                     ^
".\app\src\communication.c", line 125: Error:  #20: identifier "cache" is undefined
      cache[4] = m;
      ^
".\app\src\communication.c", line 129: Error:  #20: identifier "kWord" is undefined
      if (fdbType == kWord) {
                     ^
".\app\src\communication.c", line 137: Error:  #20: identifier "kResponse" is undefined
      } else if (fdbType == kResponse) {
                            ^
".\app\src\communication.c", line 145: Error:  #20: identifier "g_ptrCom" is undefined
      g_ptrCom.ComTransmitDmaData(cache, COM_PROTOCOL_FIX_LENGTH + len);
      ^
".\app\src\communication.c", line 145: Error:  #20: identifier "COM_PROTOCOL_FIX_LENGTH" is undefined
      g_ptrCom.ComTransmitDmaData(cache, COM_PROTOCOL_FIX_LENGTH + len);
                                         ^
".\app\src\communication.c", line 146: Warning:  #223-D: function "WaitTransmit" declared implicitly
      WaitTransmit();
      ^
".\app\src\communication.c", line 157: Error:  #142: expression must have pointer-to-object type
          s_comm_fifo.ring_buffer[s_comm_fifo.rec_count] = rec_size;
          ^
".\app\src\communication.c", line 192: Error:  #29: expected an expression
      s_comm_fifo.ring_buffer.buffer = {0};
                                       ^
".\app\src\communication.c", line 192: Error:  #137: expression must be a modifiable lvalue
      s_comm_fifo.ring_buffer.buffer = {0};
      ^
".\app\src\communication.c", line 113: Warning:  #177-D: function "CommInteractorAckMessage"  was declared but never referenced
  static void CommInteractorAckMessage(uint8_t cmd, uint8_t id, uint8_t *transmitBuff, uint16_t len, uint8_t fdbType) {
              ^
".\app\src\communication.c", line 166: Warning:  #177-D: function "ComTransmitInterruptCallback"  was declared but never referenced
  static void ComTransmitInterruptCallback(void) {
              ^
.\app\src\communication.c: 3 warnings, 9 errors
".\..\bsp\src\bsp_freq.c", line 15: Warning:  #223-D: function "MX_TIM3_Init" declared implicitly
      MX_TIM3_Init(count);
      ^
".\..\bsp\src\bsp_freq.c", line 50: Warning:  #223-D: function "motor_pwm_init" declared implicitly
      motor_pwm_init();
      ^
.\..\bsp\src\bsp_freq.c: 2 warnings, 0 errors
".\user\src\calibration_process.c", line 62: Error:  #20: identifier "TransmitData" is undefined
      g_time_task_ptr->register_tasks(0, 1, TransmitData);
                                            ^
".\user\src\calibration_process.c", line 72: Error:  #20: identifier "rec_vi_raw_data_t" is undefined
      rec_vi_raw_data_t raw_data;
      ^
".\user\src\calibration_process.c", line 85: Error:  #20: identifier "cache" is undefined
          memset(cache, 0, RANGING_FRAME_LENGTH);
                 ^
".\user\src\calibration_process.c", line 85: Error:  #20: identifier "RANGING_FRAME_LENGTH" is undefined
          memset(cache, 0, RANGING_FRAME_LENGTH);
                           ^
".\user\src\calibration_process.c", line 89: Error:  #20: identifier "g_ptrSpi" is undefined
          g_ptrSpi.SpiReadBytes(cache, RANGING_FRAME_LENGTH, (uint8_t *)&raw_data, RANGING_FRAME_LENGTH);
          ^
".\user\src\calibration_process.c", line 96: Error:  #20: identifier "g_cal_data_t" is undefined
          g_cal_data_t.raw_tof   = 0;
          ^
".\user\src\calibration_process.c", line 101: Error:  #20: identifier "g_regEncoder" is undefined
              g_cal_data_t.delta_angle = g_regEncoder.calcEdgeCountAve;
                                         ^
".\user\src\calibration_process.c", line 104: Error:  #20: identifier "g_regEncoder" is undefined
          } while (g_cal_data_t.encoder_cnt != g_regEncoder.calcEdgeNum);
                                               ^
".\user\src\calibration_process.c", line 106: Error:  #20: identifier "g_regEncoder" is undefined
          g_cal_data_t.is_start_pack = g_regEncoder.startPack;
                                       ^
".\user\src\calibration_process.c", line 126: Error:  #20: identifier "g_regVi4302" is undefined
          if (g_regVi4302.isWriteReg == true) {
              ^
".\user\src\calibration_process.c", line 130: Error:  #20: identifier "g_regVi4302" is undefined
          if (g_regVi4302.isReadReg == true) {
              ^
".\user\src\calibration_process.c", line 133: Warning:  #223-D: function "FdbMessage" declared implicitly
              FdbMessage(kD2H | kHRS | kHSS, 0xAB, (uint8_t *)&r_value, REGISTER_BYTE_CNT, kResponse);
              ^
".\user\src\calibration_process.c", line 133: Error:  #20: identifier "REGISTER_BYTE_CNT" is undefined
              FdbMessage(kD2H | kHRS | kHSS, 0xAB, (uint8_t *)&r_value, REGISTER_BYTE_CNT, kResponse);
                                                                        ^
".\user\src\calibration_process.c", line 133: Error:  #20: identifier "kResponse" is undefined
              FdbMessage(kD2H | kHRS | kHSS, 0xAB, (uint8_t *)&r_value, REGISTER_BYTE_CNT, kResponse);
                                                                                           ^
".\user\src\calibration_process.c", line 143: Error:  #20: identifier "SCALE_MUTIPLES" is undefined
          g_cal_data_t.raw_tof = (raw_data.raw_tof1) / SCALE_MUTIPLES - RAW_TOF_OFFSET;
                                                       ^
".\user\src\calibration_process.c", line 143: Error:  #20: identifier "RAW_TOF_OFFSET" is undefined
          g_cal_data_t.raw_tof = (raw_data.raw_tof1) / SCALE_MUTIPLES - RAW_TOF_OFFSET;
                                                                        ^
".\user\src\calibration_process.c", line 224: Error:  #20: identifier "spad_compensation_confidence" is undefined
          if (g_cal_data_t.noise1 > spad_compensation_confidence->rational_x_limit) {
                                    ^
".\user\src\calibration_process.c", line 228: Error:  #20: identifier "spad_compensation_confidence" is undefined
          if (g_cal_data_t.noise1 < (spad_compensation_confidence->rational_x_limit / 4) && g_cal_data_t.raw_peak1 < 600) {
                                     ^
".\user\src\calibration_process.c", line 230: Error:  #20: identifier "g_ptrRegCentroid" is undefined
              g_ptrRegCentroid.distance_tmp = 0;
              ^
".\user\src\calibration_process.c", line 249: Error:  #20: identifier "g_ptrRegCentroid" is undefined
                  g_ptrRegCentroid.distance_tmp = (uint32_t)(g_cal_data_t.raw_tof * 100);
                  ^
".\user\src\calibration_process.c", line 260: Error:  #20: identifier "g_ptrRegCentroid" is undefined
                  g_ptrRegCentroid.distance_tmp = 0;
                  ^
".\user\src\calibration_process.c", line 269: Error:  #20: identifier "DDS_LASER_PEAK_THRESHOLD" is undefined
          if (g_cal_data_t.raw_peak1 > DDS_LASER_PEAK_THRESHOLD) {
                                       ^
".\user\src\calibration_process.c", line 270: Error:  #20: identifier "g_ptrDDSInfoCount" is undefined
              g_ptrDDSInfoCount.laserCnt = getAbsolutionTime();
              ^
".\user\src\calibration_process.c", line 270: Warning:  #223-D: function "getAbsolutionTime" declared implicitly
              g_ptrDDSInfoCount.laserCnt = getAbsolutionTime();
                                           ^
".\user\src\calibration_process.c", line 277: Error:  #20: identifier "g_ptrRegCentroid" is undefined
          g_ptrRegCentroid.distance_si = ((g_ptrRegCentroid.distance_last << 16) & 0xffff0000) | (g_ptrRegCentroid.peak_last & 0x0000ffff);
          ^
".\user\src\calibration_process.c", line 438: Warning:  #223-D: function "delay_1ms" declared implicitly
      delay_1ms(100);
      ^
".\user\src\calibration_process.c", line 441: Error:  #20: identifier "g_ptrTimeTask" is undefined
          g_ptrTimeTask.TaskPoll();
          ^
".\user\src\calibration_process.c", line 442: Warning:  #223-D: function "ParseAck" declared implicitly
          ParseAck();
          ^
".\user\src\calibration_process.c", line 445: Error:  #20: identifier "g_regVi4302" is undefined
              if (g_regVi4302.isWriteReg == true) {
                  ^
".\user\src\calibration_process.c", line 447: Error:  #20: identifier "g_ptrSpi" is undefined
                  g_ptrSpi.wRegister(g_regVi4302.buff[1] | g_regVi4302.buff[2] << 8, g_regVi4302.buff[0], NULL);
                  ^
".\user\src\calibration_process.c", line 450: Error:  #20: identifier "g_regVi4302" is undefined
              if (g_regVi4302.isReadReg == true) {
                  ^
".\user\src\calibration_process.c", line 452: Error:  #20: identifier "g_ptrSpi" is undefined
                  g_ptrSpi.rRegister(g_regVi4302.buff[1] | g_regVi4302.buff[2] << 8, &rVall[0], NULL);
                  ^
".\user\src\calibration_process.c", line 453: Warning:  #223-D: function "FdbMessage" declared implicitly
                  FdbMessage(kD2H | kHRS | kHSS, 0xAB, (uint8_t *)&rVall, REGISTER_BYTE_CNT, kResponse);
                  ^
".\user\src\calibration_process.c", line 453: Error:  #20: identifier "REGISTER_BYTE_CNT" is undefined
                  FdbMessage(kD2H | kHRS | kHSS, 0xAB, (uint8_t *)&rVall, REGISTER_BYTE_CNT, kResponse);
                                                                          ^
".\user\src\calibration_process.c", line 453: Error:  #20: identifier "kResponse" is undefined
                  FdbMessage(kD2H | kHRS | kHSS, 0xAB, (uint8_t *)&rVall, REGISTER_BYTE_CNT, kResponse);
                                                                                             ^
.\user\src\calibration_process.c: 5 warnings, 30 errors

>>> ld

