/*
 * @Author: xx
 * @Date: 2024-07-31 16:37:48
 * @LastEditors: Do not edit
 * @LastEditTime: 2024-08-05 17:49:16
 * @Description:
 * @FilePath: \MDK-ARMc:\Users\<USER>\Desktop\fw01_dToF_lidar\App\inc\pid_ctrl.h
 */
#ifndef __PID_CTRL_H__
#define __PID_CTRL_H__

#include <math.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>

typedef struct {
    float speed_set;
    float speed_feedback;

    float integral;
    float err_last;
    float kp;
    float ki;
    float i_limit;
    float ctrl_out;

    uint32_t speed_set_us;
    uint32_t speed_feedback_us;

    uint32_t speed_sum_last;
    uint32_t speed_sum_cur;
    uint32_t speed_interval;
    uint16_t speed_interval_cnt;
    uint16_t speed_interval_cnt_last;
    float    speed_duty_last;
} pid_speed_t;


extern pid_speed_t g_pid_speed;


void pid_param_init(pid_speed_t *pid, uint16_t spd);
void pid_calc_loop(pid_speed_t *pid, bool is_running);

#endif
