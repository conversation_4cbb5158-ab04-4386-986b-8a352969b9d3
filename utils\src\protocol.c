#include "protocol.h"
#include "check_cal.h"


static ProtocolHandlerPtr s_content_handler_callback_ = NULL;

/**
 * @brief 协议信息
 *
 */
static const StProtocolInfo s_protocol_info[ePROT_INTERACTION + 1] = {
    [ePROT_INTERACTION] =
        {
            .header       = 0xA5,
            .ack_header   = 0xA5,
            .fixed_length = 6,
            .check_index  = 3,
            .num_multiple = 2,
        },
};

static ProtocolError ProtocolRegisterHandler(ProtocolHandlerPtr handler) {
    if (handler == NULL) {
        return PROTOCOL_HANDLER_NOT_FOUND;
    }
    s_content_handler_callback_ = handler;
    return PROTOCOL_SUCCESS;
}

static ProtocolError ProtocolDefaultHandler(const ProtocolMessage *msg) {
    // 默认处理函数
    return PROTOCOL_SUCCESS;
}

static void ProtocolInit(void) {
}

void ProtocolInteractorAckMessage(uint8_t cmd, uint8_t id, uint8_t *transmitBuff, uint16_t len, uint8_t fdbType) {


    // cache[0] = 0xA5;
    // cache[1] = cmd;
    // cache[2] = id;

    // uint16_t m = 0;
    // if (fdbType == kOk) {
    //     m = 0;
    // } else {
    //     m = len >> 1;
    // }

    // cache[4] = m;
    // cache[5] = m >> 8;
    // cache[3] = cache[0] ^ cache[1] ^ cache[2] ^ cache[4] ^ cache[5];

    // if (fdbType == kWord) {
    //     for (uint16_t n = 0; n < len; n = n + 4) {
    //         cache[6 + n]     = transmitBuff[n];
    //         cache[6 + n + 1] = transmitBuff[n + 1];
    //         cache[6 + n + 2] = transmitBuff[n + 2];
    //         cache[6 + n + 3] = transmitBuff[n + 3];
    //         cache[3] ^= cache[6 + n] ^ cache[6 + n + 1] ^ cache[6 + n + 2] ^ cache[6 + n + 3];
    //     }
    // } else if (fdbType == kResponse) {
    //     for (uint16_t n = 0; n < len; n = n + 2) {
    //         cache[6 + n]     = transmitBuff[n];
    //         cache[6 + n + 1] = transmitBuff[n + 1];
    //         cache[3] ^= cache[6 + n] ^ cache[6 + n + 1];
    //     }
    // }

    // g_ptrCom.ComTransmitDmaData(cache, COM_PROTOCOL_FIX_LENGTH + len);
    // WaitTransmit();
}

/**
 *@brief: comm data send
 *
 */
// static void ProtocolInteractorAckMessage(uint8_t cmd, uint8_t id, uint8_t *send_data, uint8_t length) {
//     uint8_t transmit_buff_length = 0;

//     s_transmit_buff.FRAME.cmd = (cmd & (uint8_t)eR) | (uint8_t)eH2D;
//     s_transmit_buff.FRAME.id  = id;
//     s_transmit_buff.FRAME.num = length;

//     memcpy(s_transmit_buff.FRAME.data_buff, send_data, length);

//     transmit_buff_length           = eFIXED_LENGTH + s_transmit_buff.FRAME.num;
//     s_transmit_buff.FRAME.checkXor = calXOR((uint8_t const *)s_transmit_buff.buffer, transmit_buff_length, (uint8_t)eXOR_INDEX);

//     uart1_send_data(s_transmit_buff.buffer, transmit_buff_length);
// }

void TransmitInfoInit(void) {
    //* transmit
    // s_transmit_buff.FRAME.header = eACK_HEADER;

    // //*
    // g_protocol_ack_flag.ack_num = 0;
}

static uint16_t ProtocolGetDataAreaNum(const StInteractionProtocol *const data_receive_) {
    return (data_receive_->num << 1);
}

static uint16_t ProtocolGetFrameLength(EProtocolType type, uint16_t num) {
    return (num * s_protocol_info[type].num_multiple) + s_protocol_info[type].fixed_length;
}

static ProtocolError ProtocolEncode(ProtocolMessage *msg, StInteractionProtocol *frame) {
    frame->header    = 0xA5;
    frame->cmd = msg->cmd;
    frame->id = msg->id;

    uint16_t m = 0;
    if (msg->ack_type == kOk) {
        m = 0;
    } else {
        m = len >> 1;
    }

    cache[4] = m;
    cache[5] = m >> 8;
    cache[3] = cache[0] ^ cache[1] ^ cache[2] ^ cache[4] ^ cache[5];

    if (fdbType == kWord) {
        for (uint16_t n = 0; n < len; n = n + 4) {
            cache[6 + n]     = transmitBuff[n];
            cache[6 + n + 1] = transmitBuff[n + 1];
            cache[6 + n + 2] = transmitBuff[n + 2];
            cache[6 + n + 3] = transmitBuff[n + 3];
            cache[3] ^= cache[6 + n] ^ cache[6 + n + 1] ^ cache[6 + n + 2] ^ cache[6 + n + 3];
        }
    } else if (fdbType == kResponse) {
        for (uint16_t n = 0; n < len; n = n + 2) {
            cache[6 + n]     = transmitBuff[n];
            cache[6 + n + 1] = transmitBuff[n + 1];
            cache[3] ^= cache[6 + n] ^ cache[6 + n + 1];
        }
    }

    return PROTOCOL_SUCCESS;
}

static ProtocolError ProtocolDecodeOneFrame(const StInteractionProtocol *const data_receive_) {
    uint8_t xor_cal = 0;

    if (data_receive_->header == s_protocol_info[ePROT_INTERACTION].header) {  //帧头
        /* num check */
        uint8_t cmd_length = ProtocolGetFrameLength(ePROT_INTERACTION, data_receive_->num);
        if (cmd_length > INTERACTION_BUFF_SIZE) {
            return PROTOCOL_LENGTH_ERROR;
        }
        /* check sum cal */
        xor_cal = calXOR((uint8_t *)data_receive_->data, cmd_length, s_protocol_info[ePROT_INTERACTION].check_index);

        if (xor_cal == data_receive_->checkXor) {
            // 创建协议消息结构
            ProtocolMessage msg = {
                .cmd    = data_receive_->cmd,
                .id     = data_receive_->id,
                .data   = data_receive_->data,
                .length = data_receive_->num << 1,
            };

            // 调用注册的处理函数
            if (s_content_handler_callback_ != NULL) {
                return s_content_handler_callback_(&msg);
            } else {
                return PROTOCOL_HANDLER_NOT_FOUND;
            }
        }
        return PROTOCOL_CHECKSUM_ERROR;
    }
    return PROTOCOL_HEADER_ERROR;
}

static ProtocolError ProtocolDecodeMultiFrame(const uint8_t *const buff_, uint16_t length) {
    const StInteractionProtocol *data_receive_ = (StInteractionProtocol *)buff_;

    for (; (uint8_t *)data_receive_ < (buff_ + length);) {
        if (ProtocolDecodeOneFrame(data_receive_) == PROTOCOL_SUCCESS) {
            data_receive_ += ProtocolGetFrameLength(ePROT_INTERACTION, data_receive_->num);
        } else {
            ++data_receive_;
        };
    }
    return PROTOCOL_SUCCESS;
}

static inline bool isValidFrame(uint16_t cache_len) {
    return cache_len >= s_protocol_info[ePROT_INTERACTION].fixed_length;
}


static StProtocolInfo getProtocolInfo(EProtocolType type) {
    if (type > ePROT_INTERACTION) {
        type = ePROT_INTERACTION;
    }
    return s_protocol_info[type];
}

void ProtocolCmdAck() {
    // uint8_t ack_try_timers = 10, i = 0;
    // if (g_protocol_ack_flag.ack_num > 0) {
    //     for (i = 0; i < CMD_ACK_CACHE_NUM; i++) {
    //         if (g_protocol_ack_flag.st_cmd_ack_flag[i].ack_id != 0 && g_protocol_ack_flag.st_cmd_ack_flag[i].ack_status) {

    //             do {
    //                 if (g_comm_flag.StFlag.is_uart0_sended) {
    //                     interactor_ack_message(g_protocol_ack_flag.st_cmd_ack_flag[i].ack_cmd,
    //                                            g_protocol_ack_flag.st_cmd_ack_flag[i].ack_id,
    //                                            g_protocol_ack_flag.st_cmd_ack_flag[i].send_buff,
    //                                            g_protocol_ack_flag.st_cmd_ack_flag[i].send_len);
    //                     memset(g_protocol_ack_flag.st_cmd_ack_flag[i].send_buff, 0, sizeof(g_protocol_ack_flag.st_cmd_ack_flag[i].send_buff));

    //                     g_protocol_ack_flag.ack_num--;
    //                     break;
    //                 }
    //             } while (ack_try_timers--);

    //             g_protocol_ack_flag.st_cmd_ack_flag[i].ack_id = 0;
    //         }
    //     }
    // }
}

const API_Protocol_T protocol_api = {
    .init                    = ProtocolInit,
    .get_protocol_info       = getProtocolInfo,
    .is_valid_frame          = isValidFrame,
    .get_frame_data_area_num = ProtocolGetDataAreaNum,
    .encode                  = ProtocolEncode,
    .decode_one_frame        = ProtocolDecodeOneFrame,
    .decode_multi_frame      = ProtocolDecodeMultiFrame,
    .register_handler        = ProtocolRegisterHandler,
};


const API_Protocol_T *const g_protocol_ptr = &protocol_api;
