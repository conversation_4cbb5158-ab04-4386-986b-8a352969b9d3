#include "device.h"
#include "protocol.h"

#include "variable_table.h"

#include "usart.h"


#if (defined A0_D4_SAMSUNG) || (defined A2_D4_SAMSUNG) || (defined A0_D4_POSITIC) || (defined A2_D4_POSITIC)

#if USING_CSPC_PROTOCOL == 1
uint8_t kDiviceInformation[27] = {0xA5, 0X5A, 20, 0, 0, 0, 1, 'C', 'O', 'I', 'N', '-', 'D', '4', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

#else
const uint8_t kDiviceInformation[61] = {0x5A, 0xAA, 0x36, 0x00, 0x00, 0x00, 0x01, 0x06, 'G',  'K',  'D',  0x4,  'M',  0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

#endif

#elif (defined A0_D4A_UMOUSE) || (defined A2_D4A_UMOUSE)
#if USING_CSPC_PROTOCOL == 1
uint8_t kDiviceInformation[27] = {0xA5, 0X5A, 20, 0, 0, 0, 1, 'C', 'O', 'I', 'N', '-', 'D', '4', 'A', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

#else
const uint8_t kDiviceInformation[61] = {0x5A, 0xAA, 0x36, 0x00, 0x00, 0x00, 0x01, 0x06, 'G',  'K',  'D',  0x4,  'M',  0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

#endif

#elif (defined A0_T4_JINGCHUANG) || (defined A2_T4_JINGCHUANG) || (defined A0_T4_WEILAN) || (defined A2_T4_WEILAN) || (defined A0_T4_TAKDIR) ||                \
    (defined A2_T4_TAKDIR) || (defined A0_T4_NARWAL) || (defined A2_T4_NARWAL) || (defined A0_NA4_XX) || (defined A2_NA4_XX)

#if USING_CSPC_PROTOCOL == 1
uint8_t kDiviceInformation[27] = {0xA5, 0X5A, 20, 0, 0, 0, 1, 'M', 'I', 'N', 'I', '-', 'T', '4', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
#else
const uint8_t kDiviceInformation[61] = {0x5A, 0xAA, 0x36, 0x00, 0x00, 0x00, 0x01, 0x06, 'G',  'K',  'D',  0x4,  'M',  0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

#endif

#elif (defined A0_T4A_MEELUX) || (defined A2_T4A_MEELUX)
#if USING_CSPC_PROTOCOL == 1
uint8_t kDiviceInformation[27] = {0xA5, 0X5A, 20, 0, 0, 0, 1, 'M', 'I', 'N', 'I', '-', 'T', '4', 'A', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
#else
const uint8_t kDiviceInformation[61] = {0x5A, 0xAA, 0x36, 0x00, 0x00, 0x00, 0x01, 0x06, 'G',  'K',  'D',  0x4,  'M',  0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

#endif
#elif (defined A0_T4B_XX) || (defined A2_T4B_XX)
#if USING_CSPC_PROTOCOL == 1
uint8_t kDiviceInformation[27] = {0xA5, 0X5A, 20, 0, 0, 0, 1, 'M', 'I', 'N', 'I', '-', 'T', '4', 'B', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
#else
const uint8_t kDiviceInformation[61] = {0x5A, 0xAA, 0x36, 0x00, 0x00, 0x00, 0x01, 0x06, 'G',  'K',  'D',  0x4,  'M',  0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

#endif
#elif (defined A0_D6_XX) || (defined A2_D6_XX)
#if USING_CSPC_PROTOCOL == 1
uint8_t kDiviceInformation[27] = {0xA5, 0X5A, 20, 0, 0, 0, 1, 'C', 'O', 'I', 'N', '-', 'D', '6', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
#else
const uint8_t kDiviceInformation[61] = {0x5A, 0xAA, 0x36, 0x00, 0x00, 0x00, 0x01, 0x06, 'G',  'K',  'D',  0x4,  'M',  0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
#endif


#elif (defined A0_T5_XX) || (defined A2_T5_XX)
#if USING_CSPC_PROTOCOL == 1
uint8_t kDiviceInformation[27] = {0xA5, 0X5A, 20, 0, 0, 0, 1, 'M', 'I', 'N', 'I', '-', 'T', '5', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
#else
const uint8_t kDiviceInformation[61] = {0x5A, 0xAA, 0x36, 0x00, 0x00, 0x00, 0x01, 0x06, 'G',  'K',  'D',  0x4,  'M',  0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

#endif

#endif


/**
 * @brief
 *
 */
static void ParseAck(void) {
    if (g_stParseAck.ackTimes > 0) {
        switch (g_stParseAck.ackId) {
        case kTofMode:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
                g_comm_ptr->comm_send_data();
                
                 PROCESS_DELAY(2);
                __set_FAULTMASK(1);  //��ֹ���еĿ������ж�
                NVIC_SystemReset();
            } else if (g_stParseAck.ackType == kWriteError) {  // kD2H|kHSS �·��ɹ�����δִ�гɹ�
                g_protocol_ptr->encode(kD2H | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                g_protocol_ptr->encode(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_ptrLidarStatus.lidarMode, SYSTEM_MODE_BYTE_CNT, kResponse);
            }
            break;
        case kMcuId:
            if (g_stParseAck.ackType == kReadOk) {
                g_protocol_ptr->encode(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_ptrSysParam.uID, MCU_ID_BYTE_CNT, kResponse);
            }
            break;
        case kVersionBuad:
            if (g_stParseAck.ackType == kReadOk) {
                g_protocol_ptr->encode(kD2H | kHRS | kHSS, g_stParseAck.ackId, g_ptrSysParam.version, VERSION_BAUD_BYTE_CNT, kResponse);
            }
            break;
        case kMeasureRange:
            /*if(g_stParseAck.ackType == kWriteOk) {
                    g_protocol_ptr->encode(kD2H|kHWS|kHSS,g_stParseAck.ackId,NULL,0,kOk);
                    PROCESS_DELAY(1);
                    NVIC_SystemReset();
            }
            else if(g_stParseAck.ackType == kWriteError) {
                    g_protocol_ptr->encode(kD2H|kHSS,g_stParseAck.ackId,NULL,0,kOk);
            }
            else if(g_stParseAck.ackType == kReadOk) {
                    g_protocol_ptr->encode(kD2H|kHRS|kHSS,g_stParseAck.ackId,(uint8_t *)&g_ptrSysParam.measureRange,MEASURE_RANGE_BYTE_CNT,kResponse);
            }*/
            break;
        case kCalibrationParam:
            if (g_stParseAck.ackType == kWriteOk) {
                // OPEN_LED1
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
                PROCESS_DELAY(2);
                __set_FAULTMASK(1);
                NVIC_SystemReset();
            } else if (g_stParseAck.ackType == kWriteError) {
                g_protocol_ptr->encode(kD2H | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                //	OPEN_LED1
                g_protocol_ptr->encode(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)CALIBRATION_ADDR, 256 * 4, kWord);
            }
            break;
        case kTgParam:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
                __set_FAULTMASK(1);
                NVIC_SystemReset();
            } else if (g_stParseAck.ackType == kWriteError) {
                g_protocol_ptr->encode(kD2H | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                g_protocol_ptr->encode(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_ptrSysParam.tgCoefficientP3, TG_BYTE_CNT, kWord);
            }
            break;
        case kReflelParam:
            /*if(g_stParseAck.ackType == kWriteOk) {
                    g_protocol_ptr->encode(kD2H|kHWS|kHSS,g_stParseAck.ackId,NULL,0,kOk);
                    PROCESS_DELAY(1);
                    NVIC_SystemReset();
            }
            else if(g_stParseAck.ackType == kWriteError) {
                    g_protocol_ptr->encode(kD2H|kHSS,g_stParseAck.ackId,NULL,0,kOk);
            }
            else if(g_stParseAck.ackType == kReadOk) {
                    //g_protocol_ptr->encode(kD2H|kHRS|kHSS,g_stParseAck.ackId,(uint8_t *)&g_ptrSysParam.materialConfficientP3,REFLECT_BYTE_CNT,kWord);
            }*/
            break;
        case kHisAndFacula:
            /*if(g_stParseAck.ackType == kWriteOk) {
                    g_protocol_ptr->encode(kD2H|kHWS|kHSS,g_stParseAck.ackId,NULL,0,kOk);
                    PROCESS_DELAY(1);
                    NVIC_SystemReset();
            }
            else if(g_stParseAck.ackType == kWriteError) {
                    g_protocol_ptr->encode(kD2H|kHSS,g_stParseAck.ackId,NULL,0,kOk);
            }
            else if(g_stParseAck.ackType == kReadOk) {
                    g_protocol_ptr->encode(kD2H|kHRS|kHSS,g_stParseAck.ackId,(uint8_t
            *)&g_ptrSysParam.histogramAndSpadSampleFreq,HIS_FAC_FREQ_BYTE_CNT,kResponse);
            }*/
            break;
        case kTemperature:
            if (g_stParseAck.ackType == kReadOk) {
                // g_ptrSysParam.currentTemperature = ((float)TEMPERATURE_TABLE[(g_ptrSysParam.currentTempAdc - 214)>>1])/10;
                g_ptrSysParam.currentTemperature = GettingTemperature(
                    GettingAdcValue());  //-0.0332f*GettingAdcValue() + 87.1023f;//((float)TEMPERATURE_TABLE[(GettingAdcValue() - 214)>>1])/10;
                g_protocol_ptr->encode(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_ptrSysParam.currentTemperature, TEMPERATURE_BYTE_CNT, kWord);
            }
            break;
        case kRegisterSetting:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            }
            break;
        case kZeroAngleSetting:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
                PROCESS_DELAY(2);
                __set_FAULTMASK(1);
                NVIC_SystemReset();
            } else if (g_stParseAck.ackType == kWriteError) {
                g_protocol_ptr->encode(kD2H | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                g_protocol_ptr->encode(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_ptrSysParam.zeroAngle, ZERO_ANGLE_BYTE_CNT, kWord);
            }
            break;
        case kLaserControl:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            }
            break;
        case kLedControl:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            }
            break;
        case kVbd:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
                PROCESS_DELAY(2);
                __set_FAULTMASK(1);
                NVIC_SystemReset();
            } else if (g_stParseAck.ackType == kReadOk) {
                g_protocol_ptr->encode(kD2H | kHRS | kHSS, g_stParseAck.ackId, &g_ptrSysParam.vbdStep, VBD_BYTE_CNT, kResponse);
            }
            break;
        case kDebugSetting:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            }
            break;
        case kDebugSetting2:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            }
            break;
        case kBootloader:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
                PROCESS_DELAY(2);
                __set_FAULTMASK(1);
                NVIC_SystemReset();
            }
            break;
        case kRangingMode:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            }
            break;
        case kXtalkCalc:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                g_protocol_ptr->encode(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_param_combine.xtalk.xtalk_bin, 8, kResponse);
            }
            break;
        // g_param_combine
        case kBtMotorCtrl:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                g_protocol_ptr->encode(kD2H | kHRS | kHSS, g_stParseAck.ackId, (uint8_t *)&g_param_combine.motor_mode.mode, 2, kResponse);
            }
            break;
        case kBtMotorCode:
            if (g_stParseAck.ackType == kWriteOk) {
                g_protocol_ptr->encode(kD2H | kHWS | kHSS, g_stParseAck.ackId, NULL, 0, kOk);
            } else if (g_stParseAck.ackType == kReadOk) {
                g_protocol_ptr->encode(kD2H | kHRS | kHSS,
                                       g_stParseAck.ackId,
                                       (uint8_t *)&g_param_combine.motor_sn.sn,
                                       (g_param_combine.motor_sn.length > 256 ? 256 : g_param_combine.motor_sn.length),
                                       kResponse);
            }
            break;
        default:
            break;
        }
        g_stParseAck.ackTimes--;
    }
}

/**
 * @brief 指令内容解析函数
 *
 * @param id
 */
static void CommandContentHandle(const ProtocolMessage *const msg) {
    static uint8_t cmdBit = 0;

    uint8_t check      = 0;
    int16_t datalength = -1;

    uint16_t n                   = 0;
    uint16_t totalNum            = 0;
    uint8_t  mode_change_buff[4] = {0x00};
    uint16_t fps_4302 = 0, fps = 0;

    FLASH_STS fmcStaus = FLASH_BUSY;

    switch ((ECmdID)msg->id) {
    case kTriMode:

        break;
    case kTofMode:
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS && g_ptrSysParam.is_unlock == true) {
            g_ptrSysParam.is_unlock = false;
            mode_change_buff[2]     = 0xA1;
            mode_change_buff[3]     = g_stComFifo.FRAME.cmd;
            if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofScanMode) { /**/
                mode_change_buff[0] = (uint8_t)kTofScanMode;
                mode_change_buff[1] = kTofScanMode >> 8;
                fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofScanMode);
            } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofFaculaMode) { /**/
                mode_change_buff[0] = (uint8_t)kTofFaculaMode;
                mode_change_buff[1] = kTofFaculaMode >> 8;

                fmcStaus = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofFaculaMode);
            } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofHistMode) { /**/
                mode_change_buff[0] = (uint8_t)kTofHistMode;
                mode_change_buff[1] = kTofHistMode >> 8;
                fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofHistMode);
            } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofStopMode) { /**/
                mode_change_buff[0] = (uint8_t)kTofStopMode;
                mode_change_buff[1] = kTofStopMode >> 8;
                fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofStopMode);
            } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofCalibrationMode) { /**/
                mode_change_buff[0] = (uint8_t)kTofCalibrationMode;
                mode_change_buff[1] = kTofCalibrationMode >> 8;
                fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofCalibrationMode);
            } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofSinglePointMode) { /**/
                mode_change_buff[0] = (uint8_t)kTofSinglePointMode;
                mode_change_buff[1] = kTofSinglePointMode >> 8;
                fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofSinglePointMode);
            } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofRangingMode) { /**/
                mode_change_buff[0] = (uint8_t)kTofRangingMode;
                mode_change_buff[1] = kTofRangingMode >> 8;
                fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofSinglePointMode);
            } else if ((g_stComFifo.FRAME.data[0] << 8 | g_stComFifo.FRAME.data[1]) == kTofRevCheckMode) { /**/
                mode_change_buff[0] = (uint8_t)kTofRevCheckMode;
                mode_change_buff[1] = kTofRevCheckMode >> 8;
                fmcStaus            = g_ptrFlash.WriteFlashSomeWord(LIDAR_MODE_ADDR, mode_change_buff, 1);
                // fmcStaus = g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofSinglePointMode);
            }

            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;

        } else if ((cmdBit & kHRS) == kHRS) {
            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kReadOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;

    case kSysParam:
        if ((g_stComFifo.FRAME.cmd & kHWS) == kHWS) {

        } else if ((g_stComFifo.FRAME.cmd & kHWS) == kHRS) {
        }
        break;

    case kMcuId: /*mcu id*/
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHRS) == kHRS) {
            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kReadOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;

    case kVersionBuad: /*version baud*/
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS) {

        } else if ((cmdBit & kHRS) == kHRS) {
            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kReadOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;
    case kMeasureRange:
        break;
    case kCalibrationParam:
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS && g_ptrSysParam.is_unlock == true) { /*д״̬*/
            g_ptrSysParam.is_unlock = false;
            fmcStaus                = g_ptrFlash.WriteFlashSomeWord(CALIBRATION_ADDR, g_stComFifo.FRAME.data, 256);

            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;

        } else if ((cmdBit & kHRS) == kHRS) {

            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kReadOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;

    case kTgParam:
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS) {
            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;

        } else if ((cmdBit & kHRS) == kHRS) {
            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kReadOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;
    case kReflelParam:
        break;
    case kHisAndFacula:
        break;
    case kTemperature:
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS) {

        } else if ((cmdBit & kHRS) == kHRS) {

            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kReadOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;

    case kRegisterSetting:
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS && g_ptrSysParam.is_unlock == true) {
            g_ptrSysParam.is_unlock = false;

            g_regVi4302.isWriteReg = true;
            g_regVi4302.buff[0]    = g_stComFifo.FRAME.data[0];
            g_regVi4302.buff[1]    = g_stComFifo.FRAME.data[1];
            g_regVi4302.buff[2]    = g_stComFifo.FRAME.data[2];

            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kWriteOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;

        } else if ((cmdBit & kHRS) == kHRS) {
            g_regVi4302.isReadReg = true;
            g_regVi4302.buff[1]   = g_stComFifo.FRAME.data[0];
            g_regVi4302.buff[2]   = g_stComFifo.FRAME.data[1];
        }
        break;
    case kZeroAngleSetting:
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS && g_ptrSysParam.is_unlock) {
            g_ptrSysParam.is_unlock = false;
            fmcStaus                = g_ptrFlash.WriteFlashSomeWord(ANGLE_ADDR, g_stComFifo.FRAME.data, 1);


            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;

        } else if ((cmdBit & kHRS) == kHRS) {

            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kReadOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;
    case kLaserControl:
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS) {
            if (g_stComFifo.FRAME.data[0] == 0x01) {
                VLD_ENABLE
            } else {
                VLD_DISABLE
            }

            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kWriteOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;
    case kLedControl:
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS) {
            if (g_stComFifo.FRAME.data[0] == 0x01) {
                OPEN_LED1
                g_ptrSysParam.close_led = false;
            } else {
                CLOSE_LED1
                g_ptrSysParam.close_led = true;
            }
            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kWriteOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;
    case kVbd: /*VBD */
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS && g_ptrSysParam.is_unlock == true) {
            g_ptrSysParam.is_unlock = false;
            Vi4302StopRanging();
            g_ptrSpi.wRegister(0x024F, 0xC0, NULL);
            delay_1ms(20);

            Vi4302ExeVbdTdc(false);
            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kWriteOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;

        } else if ((cmdBit & kHRS) == kHRS) {
            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kReadOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;
    case kDebugSetting4:
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS && g_ptrLidarStatus.lidarMode == kTofHistMode) {
            Vi4302StopRanging();
            if (g_stComFifo.FRAME.data[0] == 0x00) {
                Vi4302StartHist(kHistNoraml);
                g_ptrLidarStatus.hist_channel = kHistNoraml;
            } else if (g_stComFifo.FRAME.data[0] == 0x01) {
                Vi4302StartHist(kHistAtten);
                g_ptrLidarStatus.hist_channel = kHistAtten;
            } else if (g_stComFifo.FRAME.data[0] == 0x02) {
                Vi4302StartHist(kHistRef);
                g_ptrLidarStatus.hist_channel = kHistRef;
            }
        }
        break;
    case kBootloader: /*BOOTLOADER*/
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS && g_ptrSysParam.is_unlock == true) {
            g_ptrSysParam.is_unlock = false;
            mode_change_buff[0]     = 'l';
            mode_change_buff[1]     = 'o';
            mode_change_buff[2]     = 'a';
            mode_change_buff[3]     = 'd';
            fmcStaus                = g_ptrFlash.WriteFlashSomeWord(BOOT_ADD, mode_change_buff, 1);

            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kWriteOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;
    case kDebugSetting2:
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS) {
            if (g_stComFifo.FRAME.data[0] == 0x01) {  // g_ptrSysParam.isDebugVbd
                g_ptrSysParam.isDebugHistogram = true;
            } else if (g_stComFifo.FRAME.data[0] == 0x02) {
                g_ptrSysParam.isDebugVbd = true;  // vbd
            } else if (g_stComFifo.FRAME.data[0] == 0x03) {
                g_ptrSysParam.isDebugVbd = false;  // vbd
            } else {
                g_ptrSysParam.isDebugHistogram = false;
            }

            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kWriteOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        } else {
        }
        break;
    case kDebugSetting:
        cmdBit = g_stComFifo.FRAME.cmd;
        if ((cmdBit & kHWS) == kHWS) {
            g_ptrSysParam.is_unlock = true;
            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kWriteOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }

        break;
    case kRangingMode: /*ranging mode fps */
        cmdBit = g_stComFifo.FRAME.cmd;

        if ((cmdBit & kHWS) == kHWS) {  //&& g_ptrSysParam.is_unlock == true
            g_ptrSysParam.is_unlock = false;

            fps = g_stComFifo.FRAME.data[0] | (g_stComFifo.FRAME.data[1] << 8);
            if (fps > 4000) {
                fps = 4000;
            } else if (fps < 1) {
                fps = 1;
            }


            Vi4302StopRanging();
            FrameSetting(fps, &fps_4302);
            Vi4302StartRanging();

            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kWriteOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;

        } else if ((cmdBit & kHRS) == kHRS) {
            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kReadOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;
    case kXtalkCalc:
        cmdBit = g_stComFifo.FRAME.cmd;

        if ((cmdBit & kHWS) == kHWS && g_ptrLidarStatus.lidarMode == kTofHistMode) {
            param_combine_t param_combine;
            g_ptrFlash.ReadFlashData((uint32_t)XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t));


            if (xtalk_calc(255, &param_combine.xtalk.xtalk_bin, &param_combine.xtalk.xtalk_peak)) {
                if (param_combine.xtalk.xtalk_calc_cnt == 0xFFFF) {
                    param_combine.xtalk.xtalk_calc_cnt = 0;
                }

                // param_combine.xtalk.xtalk_peak *= 2;
                param_combine.xtalk.xtalk_calc_cnt++;

                fmcStaus              = g_ptrFlash.WriteFlashSomeWord(XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t) / 4 + 1);
                g_param_combine.xtalk = param_combine.xtalk;
                g_stParseAck.ackTimes++;
                g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
                g_stParseAck.ackId   = g_stComFifo.FRAME.id;
                Vi4302StartHist(kHistNoraml);
            }
        } else if ((cmdBit & kHRS) == kHRS) {
            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kReadOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;

#if (defined A0_T5_XX)
    case kBtMotorCtrl:
        cmdBit = g_stComFifo.FRAME.cmd;

        if ((cmdBit & kHWS) == kHWS) {
            param_combine_t param_combine;
            g_ptrFlash.ReadFlashData((uint32_t)XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t));

            if (*(uint16_t *)&g_stComFifo.FRAME.data[2] == kRunning) {
                if (param_combine.motor_mode.mode != kRunning) {
                    param_combine.motor_mode.mode = kRunning;
                    fmcStaus                      = g_ptrFlash.WriteFlashSomeWord(XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t) / 4 + 1);
                }
                g_ptrDDSInfoCount.encoder_cnt   = getAbsolutionTime();
                g_param_combine.motor_mode.mode = kRunning;
                fmcStaus                        = FLASH_EOP;

            } else if (*(uint16_t *)&g_stComFifo.FRAME.data[2] == kStandby) {
                if (param_combine.motor_mode.mode != kStandby) {
                    param_combine.motor_mode.mode = kStandby;
                    fmcStaus                      = g_ptrFlash.WriteFlashSomeWord(XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t) / 4 + 1);
                }
                g_param_combine.motor_mode.mode = kStandby;
                fmcStaus                        = FLASH_EOP;
            } else if (*(uint16_t *)&g_stComFifo.FRAME.data[2] == kTurnOn) {
                if (g_param_combine.motor_mode.mode != kRunning) {
                    g_param_combine.motor_mode.mode = kTurnOn;
                    g_ptrDDSInfoCount.encoder_cnt   = getAbsolutionTime();
                    fmcStaus                        = FLASH_EOP;
                }
            } else if (*(uint16_t *)&g_stComFifo.FRAME.data[2] == kTurnOff) {
                if (g_param_combine.motor_mode.mode != kRunning) {
                    g_param_combine.motor_mode.mode = kTurnOff;
                    fmcStaus                        = FLASH_EOP;
                }

            } else if (*(uint16_t *)&g_stComFifo.FRAME.data[2] == 0x0000 && g_stComFifo.FRAME.data[0] != 0 && g_ptrSysParam.is_unlock == true) {
                g_ptrSysParam.is_unlock          = false;
                g_param_combine.motor_mode.speed = *(uint16_t *)&g_stComFifo.FRAME.data[0];
                param_combine.motor_mode.speed   = g_param_combine.motor_mode.speed;
                pid_param_init(&g_pid_speed, g_param_combine.motor_mode.speed);
                fmcStaus = g_ptrFlash.WriteFlashSomeWord(XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t) / 4 + 1);
            }

            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;

        } else if ((cmdBit & kHRS) == kHRS) {
            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kReadOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;
    case kBtMotorCode:
        cmdBit = g_stComFifo.FRAME.cmd;

        if ((cmdBit & kHWS) == kHWS) {
            param_combine_t param_combine;

            g_ptrFlash.ReadFlashData((uint32_t)XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t));

            if (*(uint16_t *)&g_stComFifo.FRAME.data[0] <= 128) {
                memcpy(&param_combine.motor_sn, g_stComFifo.FRAME.data, g_stComFifo.FRAME.num * 2);
                fmcStaus                 = g_ptrFlash.WriteFlashSomeWord(XTALK_ADDR, (uint8_t *)&param_combine, sizeof(param_combine_t) / 4 + 1);
                g_param_combine.motor_sn = param_combine.motor_sn;
            }


            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = fmcStaus == FLASH_EOP ? kWriteOk : kWriteError;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;

        } else if ((cmdBit & kHRS) == kHRS) {
            g_param_combine.motor_sn.length = *(uint16_t *)&g_stComFifo.FRAME.data[0];
            g_stParseAck.ackTimes++;
            g_stParseAck.ackType = kReadOk;
            g_stParseAck.ackId   = g_stComFifo.FRAME.id;
        }
        break;
    case kDdsInfo:
        cmdBit = g_stComFifo.FRAME.cmd;

        if ((cmdBit & kHWS) == kHWS) {

        } else if ((cmdBit & kHRS) == kHRS) {
        }
        break;
#endif
    default:
        break;
    }

    ParseAck();
}

static void DeviceInfo(void) {
    uint16_t i, check_sum = 0;

//========================���ڹ̶��������=========================//
#if USING_CSPC_PROTOCOL == 1
    g_regLidarData0.PH           = 0x55AA;
    g_regLidarData1.PH           = 0x55AA;
    g_regLidarData2.PH           = 0x55AA;
    g_regLidarDataX.PH           = 0x55AA;
    g_regLidarData0.LSN          = 0;
    g_regLidarData1.LSN          = 0;
    g_regLidarData2.LSN          = 0;
    g_regLidarDataX.LSN          = 0;
    g_regLidarData0.BufferLen    = 0;
    g_regLidarData1.BufferLen    = 0;
    g_regLidarData2.BufferLen    = 0;
    g_regLidarDataX.BufferLen    = 0;
    g_regLidarData0.Pack_Send_En = 0;
    g_regLidarData1.Pack_Send_En = 0;
    g_regLidarData2.Pack_Send_En = 0;
    g_regLidarDataX.Pack_Send_En = 0;

#else
    g_regLidarData0.header      = 0xAA55;
    g_regLidarData0.information = LIDAR_DATA_SCAN_INFO;
    g_regLidarData0.data_number = LIDAR_DATA_NUM;
    g_regLidarData0.send_end    = 0;
    g_regLidarData1.header      = 0xAA55;
    g_regLidarData1.information = LIDAR_DATA_SCAN_INFO;
    g_regLidarData1.data_number = LIDAR_DATA_NUM;
    g_regLidarData1.send_end    = 0;
    g_regLidarData2.header      = 0xAA55;
    g_regLidarData2.information = LIDAR_DATA_SCAN_INFO;
    g_regLidarData2.data_number = LIDAR_DATA_NUM;
    g_regLidarData2.send_end    = 0;
#endif


    //========================���ڲ�������ָ��=========================//
    /*if(g_lidarDataPackCount==0)
    {
        g_regLidarDataPtr	=	&g_regLidarData0;
    }
    if(g_lidarDataPackCount==1)
    {
        g_regLidarDataPtr	=	&g_regLidarData1;
    }
    if(g_lidarDataPackCount==2)
    {
        g_regLidarDataPtr	=	&g_regLidarData2;
    }*/

    g_regLidarDataPtr = &g_regLidarData0;

    /*******transmit lidar information********/
    /*kDiviceInformation[26] = VER_BUAD[0];
    for(i=0; i<27; i++) {
        check_sum += kDiviceInformation[i];
    }
    kDiviceInformation[4] = check_sum;
    kDiviceInformation[5] = check_sum>>8;

    for(i=0; i<27; i++){
        printf("%c",kDiviceInformation[i]);
    }*/
    memcpy(cache, kDiviceInformation, sizeof(kDiviceInformation));
    cache[sizeof(kDiviceInformation) - 1] = VER_BUAD[0];
    for (i = 0; i < sizeof(kDiviceInformation); i++) {
        check_sum += cache[i];
    }
    cache[4] = (uint8_t)check_sum;
    cache[5] = (uint8_t)(check_sum >> 8);
    for (i = 0; i < sizeof(kDiviceInformation); i++) {
        printf("%c", cache[i]);
    }
}

void LidarDataBegin(void) {
// uint16_t i;
/*******transmit lidar start data*********/
#if USING_CSPC_PROTOCOL == 1
    g_comm_buff.lidar_begin.header   = eLIDAR_BEGIN_HEADER;
    g_comm_buff.lidar_begin.num      = 0;
    g_comm_buff.lidar_begin.type     = eLIDAR_TYPE_DATA;
    g_comm_buff.lidar_begin.checksum = 0x0181;  // little-endian

    // cache[0] = 0xA5;
    // cache[1] = 0x5A;
    // cache[2] = 0;
    // cache[3] = 0;
    // cache[4] = 0x80;
    // cache[5] = 0x01;
    // cache[6] = 0x81;

#else
    cache[0]                    = 0x5A;
    cache[1]                    = 0xAA;
    cache[2]                    = 0;
    cache[3]                    = 0;
    cache[4]                    = 0x85;
    cache[5]                    = 0x01;
    cache[6]                    = 0x81;

#endif

    /*for(i=0; i<7; i++){
        printf("%c",cache[i]);
    }*/
    g_ptrCom.ComTransmitDmaData(g_comm_buff.buffer, 7);
}

static void DeviceInit() {
    g_protocol_ptr->register_handler(CommandContentHandle);
}

static const API_Device_T device_api = {
    .device_init       = DeviceInit,
    .command_handle    = CommandContentHandle,
    .device_info       = DeviceInfo,
    .device_begin_info = LidarDataBegin,
};

const API_Device_T *const g_device_ptr = &device_api;
