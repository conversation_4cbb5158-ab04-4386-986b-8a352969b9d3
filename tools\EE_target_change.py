'''
Author: 
Date: 
LastEditors: 
LastEditTime: 
Description: check some files is valid or generate it after change target.
    1. add new target log whithin log file, see more details in ${workspaceFloder}/readme.txt.
    2. add Macro definition with upper target_name
FilePath: \MDK-ARM\script_file\EE_new_target_config.py
'''
#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import shutil
import subprocess
import sys

def main(target):
    print(f"Target switched to: {target}")
    # 在这里添加你的自定义操作
    # 比如：编译、上传固件等操作

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: on_target_change.py <target>")
        sys.exit(1)
    target = sys.argv[1]
    main(target)
