#include "communication.h"
#include "variable_table.h"

//*****************bsp ****************//
#include "flash.h"
#include "usart.h"

//*****************common ****************//
#include "protocol.h"
#include "ring_buff.h"

//*****************app ****************//


//***********************************  */


static StCommFifo s_comm_fifo = {
    .rec_buffe_size = {{
        .type       = ePROT_INTERACTION,
        .frame_flag = eFRAME_IDLE,
        .length     = 0,
    }},
    .rec_count      = 0,
    .ring_buffer =
        {
            .buffer = {0},
            .in     = 0,
            .out    = 0,
        },
};

/* ACK信息，只能存储一帧反馈信息 */
StParseAck g_stParseAck = {
    .ackId    = 0,
    .ackTimes = 0,
    .ackType  = 0,
};

UCommBuffer g_comm_buff = {
    .buffer = {0},
};


static inline void CommCacheCountHandle(void) {
    s_comm_fifo.rec_count--;
    s_comm_fifo.rec_buffe_size[0] = s_comm_fifo.rec_buffe_size[s_comm_fifo.rec_count];  //
}

/**
 * @brief ringbuffer中数据，全部取出解析。异常处理原则：每个rec_buffe_size缓存的内容默认是独立帧，不进行拼接。单次缓存可能存在多帧情况
 *
 */
static void CommInteractionParseLoop(void) {
    /* 解析 */
    if (s_comm_fifo.rec_count > 0 && g_stParseAck.ackTimes == 0) {  //反馈完毕后才能继续解析

#if 0  //
    uint8_t           tmp      = 0;
    uint16_t          data_len = 0;
    RingBuff_Status_t status;

    tmp = g_protocol_ptr->get_frame_min_len(ePROT_INTERACTION);  //最小帧 字节数

    /* 读取command min data */
    status =
        g_ringBuffer_ptr->ringbuff_read_specific_data(&s_comm_fifo.ring_buffer, (uint8_t *)&g_comm_buff.buffer, s_comm_fifo.rec_buffe_size[0].length, , tmp);

    if (status == RING_BUFF_OK) {
        data_len = g_protocol_ptr->get_frame_data_area_num(&g_comm_buff.interaction);

        /* get command area data */
        if (data_len < tmp) {  // 读取字节 < 剩余字节数，连续帧
            g_ringBuffer_ptr->ringbuff_read_data(&s_comm_fifo.ring_buffer, (uint8_t *)g_comm_buff.interaction.data, data_len);
            s_comm_fifo.rec_buffe_size[0] = tmp - data_len;  //继续解析
        } else {
            g_ringBuffer_ptr->ringbuff_read_data(&s_comm_fifo.ring_buffer, (uint8_t *)g_comm_buff.interaction.data, data_len);  //读取剩余的字节
            CommCacheCountHandle();
        }

        /* 解析数据 */
        g_protocol_ptr->decode_one_frame(&g_comm_buff.interaction);  //
    } else {                                                         //异常，退出。异常时，已经读取了s_comm_fifo.rec_buffe_size[0]个字节
        CommCacheCountHandle();
    }

#else  //
        /* read all data*/
        if (s_comm_fifo.rec_buffe_size[0].length < Com_BUFF_SIZE) {
            g_ringBuffer_ptr->ringbuff_read_data(&s_comm_fifo.ring_buffer, (uint8_t *)g_comm_buff.buffer, s_comm_fifo.rec_buffe_size[0].length);  //读取所有字节
        }

        /* parse all frame*/
        g_protocol_ptr->decode_multi_frame(g_comm_buff.buffer, s_comm_fifo.rec_buffe_size[0].length);

        /* update rec index  */
        CommCacheCountHandle();

#endif
    }
}

static void CommAckMessage(uint16_t size) {
    UART2_SendData(g_comm_buff.buffer, size);
    WaitTransmit();
}

/**
 * @brief 端口数据接收回调函数，
 *
 * @param recSize
 */
static void ComRecInterruptCallback(uint16_t rec_size, uint8_t *rec_buff) {
    g_ringBuffer_ptr->ringbuff_write_data(&s_comm_fifo.ring_buffer, rec_buff, rec_size);
    if (s_comm_fifo.rec_count < RING_BUFF_REC_MAX_TIMES) {
        s_comm_fifo.ring_buffer[s_comm_fifo.rec_count] = rec_size;
        s_comm_fifo.rec_count++;
    }
}

/**
 * @brief
 *
 */
static void ComTransmitInterruptCallback(void) {
    // g_ptrSysFlag.isComTransmitFinished = true;
    // CLOSE_LED1
}

void CommInit(ECommType type, uint32_t param1) {
    /* init port */
    switch (type) {
    case eUART:
        USART2_Init(param1);  //

        USART2_RecHandlePtrRegister(ComRecInterruptCallback);
        break;
    default:
        break;
    }

    /* init fifo */

    /* init param */
    // g_stParseAck.ackId = 0;
    g_stParseAck.ackTimes = 0;

    s_comm_fifo.rec_count          = 0;
    s_comm_fifo.ring_buffer.in     = 0;
    s_comm_fifo.ring_buffer.out    = 0;
    s_comm_fifo.ring_buffer.buffer = {0};
}

static const API_Communication_T comm_api_ptr = {
    .comm_init            = CommInit,
    .comm_data_parse_loop = CommInteractionParseLoop,
    .comm_transmit_data   = CommAckMessage,
};

const API_Communication_T *const g_comm_ptr = &comm_api_ptr;