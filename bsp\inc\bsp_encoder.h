#ifndef _BSP_ENCODER_H
#define _BSP_ENCODER_H

#include "variable_table.h"


#ifdef __cplusplus
 extern "C" {
#endif


static void EncoderMarkEdgeCap(void);
static void EncoderMarkEdgeInterruptPriority(uint8_t mainPrio, uint8_t subPrio);
static void EncoderCalcEdgeCap(void);
static void EncoderCalcEdgeInterruptPriority(uint8_t mainPrio, uint8_t subPrio);
_Bool RegisterEncoderCallbackFunc(PtrEncoder *ptr,void (*markEdgeCallback)(uint16_t), void (*calcEdgeUpdataCallback)(void), void (*calcEdgeCallback)(uint16_t), uint32_t addr);

#ifdef __cplusplus
           }
#endif

#endif
