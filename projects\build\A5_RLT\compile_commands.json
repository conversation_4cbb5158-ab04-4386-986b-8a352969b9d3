[{"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\bsp\\src\\GPIO.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\GPIO.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\GPIO.d .\\..\\bsp\\src\\GPIO.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\bsp\\src\\adc.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\adc.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\adc.d .\\..\\bsp\\src\\adc.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\bsp\\src\\bsp_freq.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\bsp_freq.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\bsp_freq.d .\\..\\bsp\\src\\bsp_freq.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\bsp\\src\\delay.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\delay.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\delay.d .\\..\\bsp\\src\\delay.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\bsp\\src\\flash.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\flash.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\flash.d .\\..\\bsp\\src\\flash.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\bsp\\src\\spi.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\spi.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\spi.d .\\..\\bsp\\src\\spi.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\bsp\\src\\timer.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\timer.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\timer.d .\\..\\bsp\\src\\timer.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\bsp\\src\\usart.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\usart.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\usart.d .\\..\\bsp\\src\\usart.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\bsp\\src\\wdt.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\wdt.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\bsp\\src\\wdt.d .\\..\\bsp\\src\\wdt.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\CMSIS\\device\\startup\\startup_n32g401.s", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armasm.exe\" --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc --cpu Cortex-M4.fp --li --pd \"__MICROLIB SETA 1\" -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\CMSIS\\device\\startup\\startup_n32g401.o --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\CMSIS\\device\\startup\\startup_n32g401.d .\\..\\firmware\\CMSIS\\device\\startup\\startup_n32g401.s"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\CMSIS\\device\\system_n32g401.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\CMSIS\\device\\system_n32g401.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\CMSIS\\device\\system_n32g401.d .\\..\\firmware\\CMSIS\\device\\system_n32g401.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\misc.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\misc.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\misc.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\misc.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_adc.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_adc.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_adc.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_adc.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_beeper.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_beeper.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_beeper.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_beeper.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_comp.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_comp.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_comp.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_comp.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_crc.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_crc.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_crc.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_crc.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dbg.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dbg.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dbg.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dbg.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dma.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dma.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dma.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_dma.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_exti.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_exti.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_exti.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_exti.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_flash.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_flash.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_flash.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_flash.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_gpio.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_gpio.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_gpio.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_gpio.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_i2c.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_i2c.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_i2c.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_i2c.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_iwdg.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_iwdg.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_iwdg.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_iwdg.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_lptim.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_lptim.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_lptim.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_lptim.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_pwr.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_pwr.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_pwr.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_pwr.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rcc.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rcc.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rcc.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rcc.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rtc.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rtc.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rtc.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_rtc.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_spi.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_spi.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_spi.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_spi.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_tim.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_tim.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_tim.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_tim.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_usart.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_usart.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_usart.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_usart.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\firmware\\n32g401_std_periph_driver\\src\\n32g401_wwdg.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_wwdg.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\firmware\\n32g401_std_periph_driver\\src\\n32g401_wwdg.d .\\..\\firmware\\n32g401_std_periph_driver\\src\\n32g401_wwdg.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\utils\\src\\pid_ctrl.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\utils\\src\\pid_ctrl.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\utils\\src\\pid_ctrl.d .\\..\\utils\\src\\pid_ctrl.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\utils\\src\\proj_version.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\utils\\src\\proj_version.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\utils\\src\\proj_version.d .\\..\\utils\\src\\proj_version.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\utils\\src\\protocol.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\utils\\src\\protocol.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\utils\\src\\protocol.d .\\..\\utils\\src\\protocol.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\utils\\src\\ring_buff.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\utils\\src\\ring_buff.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\utils\\src\\ring_buff.d .\\..\\utils\\src\\ring_buff.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\utils\\src\\task_process.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\utils\\src\\task_process.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\utils\\src\\task_process.d .\\..\\utils\\src\\task_process.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\utils\\src\\time_tasks.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\__\\utils\\src\\time_tasks.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\__\\utils\\src\\time_tasks.d .\\..\\utils\\src\\time_tasks.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\VI4302 API\\BIN\\fw_44_00_00_80_R00.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\A5_RLT\\.obj\\VI4302 API\\BIN\\fw_44_00_00_80_R00.o\" --no_depend_system_headers --depend \".\\build\\A5_RLT\\.obj\\VI4302 API\\BIN\\fw_44_00_00_80_R00.d\" \".\\VI4302 API\\BIN\\fw_44_00_00_80_R00.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\VI4302 API\\src\\A2_Configurable.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\A5_RLT\\.obj\\VI4302 API\\src\\A2_Configurable.o\" --no_depend_system_headers --depend \".\\build\\A5_RLT\\.obj\\VI4302 API\\src\\A2_Configurable.d\" \".\\VI4302 API\\src\\A2_Configurable.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\VI4302 API\\src\\User_Driver.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\A5_RLT\\.obj\\VI4302 API\\src\\User_Driver.o\" --no_depend_system_headers --depend \".\\build\\A5_RLT\\.obj\\VI4302 API\\src\\User_Driver.d\" \".\\VI4302 API\\src\\User_Driver.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\VI4302 API\\src\\VI4302_Config.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\A5_RLT\\.obj\\VI4302 API\\src\\VI4302_Config.o\" --no_depend_system_headers --depend \".\\build\\A5_RLT\\.obj\\VI4302 API\\src\\VI4302_Config.d\" \".\\VI4302 API\\src\\VI4302_Config.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\VI4302 API\\src\\VI4302_Handle.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\A5_RLT\\.obj\\VI4302 API\\src\\VI4302_Handle.o\" --no_depend_system_headers --depend \".\\build\\A5_RLT\\.obj\\VI4302 API\\src\\VI4302_Handle.d\" \".\\VI4302 API\\src\\VI4302_Handle.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\VI4302 API\\src\\VI4302_System.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\A5_RLT\\.obj\\VI4302 API\\src\\VI4302_System.o\" --no_depend_system_headers --depend \".\\build\\A5_RLT\\.obj\\VI4302 API\\src\\VI4302_System.d\" \".\\VI4302 API\\src\\VI4302_System.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\VI4302 API\\src\\data_handle.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o \".\\build\\A5_RLT\\.obj\\VI4302 API\\src\\data_handle.o\" --no_depend_system_headers --depend \".\\build\\A5_RLT\\.obj\\VI4302 API\\src\\data_handle.d\" \".\\VI4302 API\\src\\data_handle.c\""}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\app\\src\\communication.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\app\\src\\communication.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\app\\src\\communication.d .\\app\\src\\communication.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\user\\src\\calibration_process.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\user\\src\\calibration_process.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\user\\src\\calibration_process.d .\\user\\src\\calibration_process.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\user\\src\\device.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\user\\src\\device.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\user\\src\\device.d .\\user\\src\\device.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\user\\src\\facula_process.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\user\\src\\facula_process.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\user\\src\\facula_process.d .\\user\\src\\facula_process.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\user\\src\\histogram_process.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\user\\src\\histogram_process.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\user\\src\\histogram_process.d .\\user\\src\\histogram_process.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\user\\src\\main.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\user\\src\\main.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\user\\src\\main.d .\\user\\src\\main.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\user\\src\\n32g401_it.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\user\\src\\n32g401_it.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\user\\src\\n32g401_it.d .\\user\\src\\n32g401_it.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\user\\src\\range_process.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\user\\src\\range_process.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\user\\src\\range_process.d .\\user\\src\\range_process.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\user\\src\\scan_process.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\user\\src\\scan_process.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\user\\src\\scan_process.d .\\user\\src\\scan_process.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\user\\src\\tasks_polling.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\user\\src\\tasks_polling.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\user\\src\\tasks_polling.d .\\user\\src\\tasks_polling.c"}, {"directory": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "file": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\user\\src\\variable_table.c", "command": "\"D:\\Programs\\keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I. -I../firmware/CMSIS/device/startup -I../firmware/CMSIS/device -I../firmware/CMSIS/core -I../firmware/n32g401_std_periph_driver/inc -I.cmsis/include -IRTE/_APP -I../bsp/inc -I../utils/inc -Iapp/cmd_handle -Iapp/uartfifo -Iapp/work_mode -Iapp/inc -I\"VI4302 API/BIN\" -I\"VI4302 API/inc\" -Iuser/inc -D\"N32G401\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\A5_RLT\\.obj\\user\\src\\variable_table.o --no_depend_system_headers --depend .\\build\\A5_RLT\\.obj\\user\\src\\variable_table.d .\\user\\src\\variable_table.c"}]