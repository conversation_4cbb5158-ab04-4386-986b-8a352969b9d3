#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/7/30 16:46
# <AUTHOR> name
# @File    : EE_rename_hex_name.py

import re
import shutil
import subprocess
import sys
import os
import configparser
import argparse
from datetime import datetime


################ 项目版本格式 ###################
# volatile const uint8_t code FW_PROJ_NAME[FW_PROJ_NAME_LEN]       = {'N', 'o', 'v', 'a', '-', 'A', '1', 'K', 31};
# volatile const uint8_t code FW_VERSION_NAME[FW_VERSION_NAME_LEN] = {1, 0, 1, 0, 23, 11, 6};
###############################################

def extract_constants_51(file_path, proj_var_name, customId_var_name, sv_var_name, hv_var_name, date_var_name):
    with open(file_path, "r", encoding="utf-8") as file:
        content = file.read()

    proj_name_match = re.search(r"volatile\s+const\s+uint8_t\s+code\s+" + proj_var_name + r"\[.*?\]\s*=\s*\{(.*?)\};", content)
    customer_id_match = re.search(r"volatile\s+const\s+uint8_t\s+code\s" + customId_var_name + r"\[.*?\]\s*=\s*\{(.*?)\};", content)
    sv_match = re.search(r"volatile\s+const\s+uint8_t\s+code\s+" + sv_var_name + r"\[.*?\]\s*=\s*\{(.*?)\};", content)
    hv_match = re.search(r"volatile\s+const\s+uint8_t\s+code\s+" + hv_var_name + r"\[.*?\]\s*=\s*\{(.*?)\};", content)
    date_match = re.search(r"volatile\s+const\s+uint8_t\s+code\s+" + date_var_name + r"\[.*?\]\s*=\s*\{(.*?)\};", content)

    if not proj_name_match or not customer_id_match or not sv_match or not hv_match or not date_match:
        raise ValueError("Could not find project or version or date or custom id in the source file")

    proj_name_literals = re.findall(r"'(.)'", proj_name_match.group(1))
    customer_id_literal = re.findall(r"(\d+)", customer_id_match.group(1))
    sv_literals = re.findall(r"(\d+)", sv_match.group(1))
    hv_literals = re.findall(r"(\d+)", hv_match.group(1))
    date_literals = re.findall(r"(\d+)", date_match.group(1))

    proj_name = "".join(proj_name_literals)
    customer_id = customer_id_literal[0]
    sv_numbers = [int(num) for num in sv_literals]
    hv_numbers = [int(num) for num in hv_literals]
    date_numbers = [int(num) for num in date_literals]


    constants = f"{proj_name}-{customer_id}_{sv_numbers[0]}.{sv_numbers[1]}.{sv_numbers[2]}.{hv_numbers[0]}_" \
                f"{date_numbers[0]}.{date_numbers[1]}.{date_numbers[2]}" #.{version_numbers[7]}

    return constants

def extract_constants(file_path, proj_var_name, customId_var_name, customerID_prefix, sv_var_name, hv_var_name, date_var_name, fw_type):
    with open(file_path, "r", encoding="utf-8") as file:
        content = file.read()

    # Try new struct format first
    proj_name_match = re.search(r"\.\s*" + re.escape(proj_var_name) + r"\s*=\s*\{([^}]+)\}", content)
    customer_id_match = re.search(r"\.\s*" + re.escape(customId_var_name) + r"\s*=\s*\{([^}]+)\}", content)
    sv_match = re.search(r"\.\s*" + re.escape(sv_var_name) + r"\s*=\s*\{([^}]+)\}", content)
    hv_match = re.search(r"\.\s*" + re.escape(hv_var_name) + r"\s*=\s*\{([^}]+)\}", content)
    date_match = re.search(r"\.\s*" + re.escape(date_var_name) + r"\s*=\s*\{([^}]+)\}", content)

    # Fall back to old format if new format not found
    if not proj_name_match:
        proj_name_match = re.search(r"const\s+uint8_t\s+" + re.escape(proj_var_name) + r"\[.*?\]\s*__attribute__\(\(at\(.*?\)\)\)\s*=\s*\{(.*?)\};", content)
        if not proj_name_match:
            proj_name_match = re.search(r"const\s+uint8_t\s+" + re.escape(proj_var_name) + r"\[.*?\]\s*=\s*\{(.*?)\};", content)
    
    if not customer_id_match:
        customer_id_match = re.search(r"const\s+uint8_t\s+" + re.escape(customId_var_name) + r"\[.*?\]\s*__attribute__\(\(at\(.*?\)\)\)\s*=\s*\{(.*?)\};", content)
        if not customer_id_match:
            customer_id_match = re.search(r"const\s+uint8_t\s+" + re.escape(customId_var_name) + r"\[.*?\]\s*=\s*\{(.*?)\};", content)
    
    if not sv_match:
        sv_match = re.search(r"const\s+uint8_t\s+" + re.escape(sv_var_name) + r"\[.*?\]\s*__attribute__\(\(at\(.*?\)\)\)\s*=\s*\{(.*?)\};", content)
        if not sv_match:
            sv_match = re.search(r"const\s+uint8_t\s+" + re.escape(sv_var_name) + r"\[.*?\]\s*=\s*\{(.*?)\};", content)
    
    if not hv_match:
        hv_match = re.search(r"const\s+uint8_t\s+" + re.escape(hv_var_name) + r"\[.*?\]\s*__attribute__\(\(at\(.*?\)\)\)\s*=\s*\{(.*?)\};", content)
        if not hv_match:
            hv_match = re.search(r"const\s+uint8_t\s+" + re.escape(hv_var_name) + r"\[.*?\]\s*=\s*\{(.*?)\};", content)
    
    if not date_match:
        date_match = re.search(r"const\s+uint8_t\s+" + re.escape(date_var_name) + r"\[.*?\]\s*__attribute__\(\(at\(.*?\)\)\)\s*=\s*\{(.*?)\};", content)
        if not date_match:
            date_match = re.search(r"const\s+uint8_t\s+" + re.escape(date_var_name) + r"\[.*?\]\s*=\s*\{(.*?)\};", content)

    if not proj_name_match or not customer_id_match or not sv_match or not hv_match or not date_match:
        raise ValueError("Could not find project or version or date or custom id in the source file")

    proj_name_literals = re.findall(r"'(.)'", proj_name_match.group(1))
    # customer_id_literal = re.findall(r"(\d+)", customer_id_match.group(1))
    customer_id_literals = re.findall(r"'(.)'", customer_id_match.group(1))
    sv_literals = re.findall(r"(\d+)", sv_match.group(1))
    hv_literals = re.findall(r"(\d+)", hv_match.group(1))
    date_literals = re.findall(r"(\d+)", date_match.group(1))

    proj_name = "".join(proj_name_literals)
    # customer_id = customer_id_literal[0]
    customer_id = "".join(customer_id_literals)
    # print(f"proj name:", {proj_name}) #
    # print(f"id:", {customer_id}) #    
    sv_numbers = [int(num) for num in sv_literals]
    hv_numbers = [int(num) for num in hv_literals]
    date_numbers = [int(num) for num in date_literals]
    
    # if fw_type == "test":
    #     current_date = datetime.now()
    #     year = current_date.year
    #     month = current_date.month
    #     day = current_date.day
    #     constants = f"{proj_name}_{customerID_prefix}{customer_id}_{sv_numbers[0]}.{sv_numbers[1]}.{sv_numbers[2]}.{hv_numbers[0]}_" \
    #                 f"{year}.{month}.{day}_{fw_type}" #.{version_numbers[7]}
    # else:
    constants = f"{proj_name}_{customerID_prefix}{customer_id}_{sv_numbers[0]}.{sv_numbers[1]}.{sv_numbers[2]}.{hv_numbers[0]}_" \
                    f"{date_numbers[0]}.{date_numbers[1]}.{date_numbers[2]}_{fw_type}" #.{version_numbers[7]}

    return constants

def generate_hex_file(source_file, hex_file):
    command = ["gcc", "-E", source_file, "-o", hex_file]
    subprocess.run(command)

def read_config(config_file):
    config = configparser.ConfigParser()
    config.read(config_file)

    required_options = ["proj_ver_file_path", "proj_ver_file_name", "proj_var_name", "sv_var_name", "hv_var_name", "date_var_name", "customerID_var_name"]
    missing_options = [opt for opt in required_options if not config.has_option("PROJECT_VER", opt)]
    
    if missing_options:
        raise ValueError(f"Missing required options in config file: {', '.join(missing_options)}")
    

    return {
        "proj_ver_file_path": config.get("PROJECT_VER", "proj_ver_file_path"),
        "proj_ver_file_name": config.get("PROJECT_VER", "proj_ver_file_name"),
        "proj_var_name": config.get("PROJECT_VER", "proj_var_name"),
        "sv_var_name": config.get("PROJECT_VER", "sv_var_name"),        
        "hv_var_name": config.get("PROJECT_VER", "hv_var_name"),
        "date_var_name": config.get("PROJECT_VER", "date_var_name"),
        "customerID_var_name": config.get("PROJECT_VER", "customerid_var_name"),
        "customerID_prefix": config.get("PROJECT_VER", "customerid_prefix"),
        "fw_type": config.get("PROJECT_VER", "fw_type")
    }

def write_config(config_file):
    config = configparser.ConfigParser()
    config.read(config_file)

    required_options = ["fw_type"]
    missing_options = [opt for opt in required_options if not config.has_option("PROJECT_VER", opt)]
    
    if missing_options:
        raise ValueError(f"Missing required options in config file: {', '.join(missing_options)}")
    
    # 写入新的值到 fw_type
    config.set("PROJECT_VER", "fw_type", "test")
    
    # 将修改后的配置写回文件
    with open(config_file, 'w') as configfile:
        config.write(configfile)

    # return {
    #     "fw_type": config.get("PROJECT_VER", "fw_type")
    # }

if __name__ == "__main__":
    # args = sys.argv
    # if len(args) != 4:
    #     print("Usage: python EE_rename_hex_name.py <project_name> <file_base_name>")
    #     sys.exit(1)
    # ori_project_name = args[2]
    # target_name = args[1]
    # print(f"args: {args[1]}, {args[2]}, {args[3]}")

    parser = argparse.ArgumentParser(description="Process some tasks.")
    parser.add_argument('--target', type=str, required=True, help='The target name')
    parser.add_argument('--project', type=str, required=True, help='The project name')
    # parser.add_argument('--task', type=str, required=True, help='the fw type')
    args = parser.parse_args()
    target_name = args.target
    project_name = args.project
    # fw_type = args.task
    # print(f"args:", {args.task}) #跨脚本传递，有问题

    # FW_TYPE_ENV = os.getenv('FW_TYPE')
    # print(f"args:", {args.target}, {args.project}, {FW_TYPE_ENV}) #跨脚本传递，有问题

    # if args.task == "rebuild":
    #     print("Executing rebuild task")
    #     print(f"target: {args.target}, Project: {args.project}")
    #     hex_name_type = "test"        
    # elif args.task == "commit-parallel":
    #     print("Executing commit-parallel task")
    #     print(f"target: {args.target}, Project: {args.project}")
    #     hex_name_type = "test"        
    # else:
    #     print("Unknown task")
    #
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(script_dir)    
    config_path = os.path.join(script_dir, 'config.ini')

    # get target or project name
    # GET_TARGET_PROJ_SCRIPT = os.path.join(script_dir, "EE_get_target_proj_name.py")
    # result = subprocess.run([sys.executable, GET_TARGET_PROJ_SCRIPT, project_dir], capture_output=True, text=True, check=True)
    # target_proj_config = result.stdout.strip().split(',')
    # target_name = target_proj_config[0]
    # project_name = target_proj_config[1]
    # print("target_name: ", {target_name}, " project_name:", {project_name})

    # 读取项目版本默认参数
    config = read_config(config_path)
    proj_var_name = config["proj_var_name"] # 项目变量名
    # print(f"proj variable name: {proj_var_name}")  # 添加调试信息
    sv_var_name = config["sv_var_name"] # 版本变量名
    hv_var_name = config["hv_var_name"] # 版本变量名
    date_var_name = config["date_var_name"] # 版本变量名
    # print(f"version variable name: {date_var_name}")  # 添加调试信息
    customId_var_name = config["customerID_var_name"] # 版本变量名
    customerID_prefix = config["customerID_prefix"]
    origin_hex_file_name = project_name # args.project # 原始hex文件名
    fw_type = "test" # config["fw_type"]
    # print("origin_hex_file_name: ", {project_name})

    # 解析版本信息
    proj_file_path = os.path.abspath(os.path.join(script_dir, config["proj_ver_file_path"])) # 项目版本文件路径
    # print(f"Reading proj_version file from: {proj_file_path}")  # 添加调试信息        
    if proj_file_path != "":
        constants = extract_constants(proj_file_path, proj_var_name, customId_var_name, customerID_prefix, sv_var_name, hv_var_name, date_var_name, fw_type)

        # generate_hex_file(source_file_path, hex_file_name)
        print(f"Generated file name: {constants}") #_{fw_type}

        # copy hex and bin file to version_file_name
        shutil.copyfile(
            f"./build/{target_name}/{origin_hex_file_name}.hex", f"./build/{target_name}/{constants}.hex" #_{fw_type}
        )
        # os.remove(f"./build/{target_name}/{origin_hex_file_name}.hex")
        # shutil.copyfile(
        #     f"./build/{args[1]}/{origin_hex_file_name}.bin", f"./build/{args[1]}/{constants}.bin"
        # )
        write_config(config_path)
    else:
        print("no file")
