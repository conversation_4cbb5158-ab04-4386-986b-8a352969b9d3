#ifndef _GPIO_H
#define _GPIO_H

#include "n32g401.h"


#define RESET_4302_LOW   GPIO_Pins_Reset(GPIOB, GPIO_PIN_7);
#define RESET_4302_HIGHT GPIO_Pins_Set(GPIOB, GPIO_PIN_7);


#if USING_COIN_INFORMATION == 1

#define VLD_ENABLE  GPIO_Pins_Set(GPIOB, GPIO_PIN_0);
#define VLD_DISABLE GPIO_Pins_Reset(GPIOB, GPIO_PIN_0);


#define OPEN_LED1   GPIO_Pins_Reset(GPIOA, GPIO_PIN_1);
#define CLOSE_LED1  GPIO_Pins_Set(GPIOA, GPIO_PIN_1);
#define TOGGLE_LED1 GPIO_Pin_Toggle(GPIOA, GPIO_PIN_1);


#else
#define VLD_ENABLE  GPIO_Pins_Set(GPIOA, GPIO_PIN_1);
#define VLD_DISABLE GPIO_Pins_Reset(GPIOA, GPIO_PIN_1);

#define OPEN_LED1
#define CLOSE_LED1
#define TOGGLE_LED1

#endif


void        NVIC_Configuration(uint8_t irq_val, uint8_t dis_enable, uint8_t preemptionPriority, uint8_t subPriority);
ErrorStatus SetSysClockToPLL(void);
void        RCO_OutputInit(void);
void        GPIO_Init(void);


void TX_En(void);
void TX_Disen(void);
void VI4302_En(void);
void VI4302_Disen(void);


#endif
