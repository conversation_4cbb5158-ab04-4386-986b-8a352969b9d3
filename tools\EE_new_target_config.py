'''
Author: 
Date: 
LastEditors: 
LastEditTime: 
Description: check some files is valid or generate it after change target.
    1. add new target log whithin log file, see more details in ${workspaceFloder}/readme.txt.
    2. add Macro definition with upper target_name
FilePath: \MDK-ARM\script_file\EE_new_target_config.py
'''
#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import shutil
import subprocess
import sys


def extract_constants(file_path, pre_target):
    constants: str

    with open(file_path, "r") as file:

        file_iter = iter(file)
        try:
            while True:
                # 读取当前行
                line = next(file_iter)
                # Use regular expression to find literals
                literals = re.findall(r"(\b\w+\b)", line)
                if len(literals) == 2 and literals[1] == pre_target:
                    next_line = next(file_iter)
                    if "FW_VERSION_NAME" in next_line:
                        # 提取 FW_VERSION_NAME 数组中的字符和数字
                        array_content = re.search(r"=\s*\{([^}]*)\}", next_line).group(1)
                        literals_wl = re.findall(r"'(.)'|(\d+)", array_content)
                        
                        # 提取字符和数字，并按照顺序拼接成最终字符串
                        chars = [literal[0] for literal in literals_wl[:8]]
                        numbers = [literal[1] for literal in literals_wl[8:]]

                        # 构建字符串
                        constants = "".join(chars)  # 
                        constants += f"_{numbers[0]}.{numbers[1]}.{numbers[2]}.{numbers[3]}"  # '1.0.1.0'
                        constants += f"_{int(numbers[4])}.{int(numbers[5])}.{int(numbers[6])}.{int(numbers[7])}"  # '*********'
                    return constants

        except StopIteration:
            # 当迭代器耗尽时，会触发 StopIteration 异常
            print("End of file")

    return constants


def generate_hex_file(source_file, hex_file):
    command = ["gcc", "-E", source_file, "-o", hex_file]
    subprocess.run(command)


if __name__ == "__main__":
    args = sys.argv
    print("execute script")
    # 打印脚本名称
    print("脚本名称: ", args[0], ", 参数: ", args[1])
    source_file_path = (
        "../App/src/variables.c"  # "./script_file/variable_table.c" #
    )
    if source_file_path != "":
        constants = extract_constants(source_file_path, args[1])

        # generate_hex_file(source_file_path, hex_file_name)
        print(f"Generated file name: {constants}")

    else:
        print("no file")
