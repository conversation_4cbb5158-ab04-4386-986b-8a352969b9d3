{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build",
            "type": "shell",
            "command": "${command:eide.project.build}",
            "group": "build",
            "problemMatcher": []
        },
        {
            "label": "flash",
            "type": "shell",
            "command": "${command:eide.project.uploadToDevice}",
            "group": "build",
            "problemMatcher": []
        },
        {
            "label": "build and flash",
            "type": "shell",
            "command": "${command:eide.project.buildAndFlash}",
            "group": "build",
            "problemMatcher": []
        },
        {
            "label": "rebuild",
            "type": "shell",
            "command": "${command:eide.project.rebuild}",
            "group": "build",
            "problemMatcher": []
        },
        {
            "label": "clean",
            "type": "shell",
            "command": "${command:eide.project.clean}",
            "group": "build",
            "problemMatcher": []
        },
        {
            "label": "commit-release",
            "dependsOrder": "sequence",
            "dependsOn": [
                "git-add",
                "git-commit",
                "rebuild",
            ]
        },
        {
            "label": "git-add",
            "type": "shell",
            "command": "git",
            "args": [
                "add",
                "."
            ],
            "problemMatcher": []
        },
        {
            "label": "git-commit",
            "type": "shell",
            "command": "git",
            "args": [
                "commit",
                "-F",
                "${workspaceFolder}\\cur_msg.txt"
            ],
            "problemMatcher": []
        },
    ]
}