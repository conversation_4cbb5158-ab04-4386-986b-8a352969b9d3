#ifndef _SCAN_PROCESS_H
#define _SCAN_PROCESS_H

#include "variable_table.h"

typedef struct {
    uint8_t header;
    uint8_t id;
    uint16_t time_diff;
    uint16_t distance;
    uint16_t peak;
    
    uint8_t  check_sum;


}sp_data_t;



void ScanProcess(void);
void DeviceInfo(void);
void LidarDataBegin(void);
void FillYjLidarBuf(PtrRegLidarData lidarInfo,uint8_t *sendbuff);
static uint8_t Lidar_ChecksumCal(PtrRegLidarData uartbuff,uint8_t *sendbuff);
void TransmitData(void);
void RunningScan(void);
void DDSCodePackage(uint8_t *buff);
void sp_data_transmit(void);

#endif

