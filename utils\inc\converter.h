#ifndef CONVERTER_H__
#define CONVERTER_H__

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif  // __cplusplus


#define SET_8BIT(x) ((uint8_t)((uint8_t)0x01 << (x)))
#define CLEAR_8BIT(x) ~SET_8BIT(x)
#define SET_8BITS(start, end) ((0xFF << (start)) & (0xFF >> (7 - (uint8_t)(end))))
#define CLEAR_8BITS(start, end) ~SET_8BITS(start, end)
#define GET_8BITS(val, start, end) (((val) & SET_8BITS((start), (end))) >> (start))

#define SET_16BIT(x) ((uint16_t)((uint16_t)0x01 << (x)))
#define CLEAR_16BIT(x) ~SET_16BIT(x)
#define SET_16BITS(start, end) ((0xFFFF << (start)) & (0xFFFF >> (15 - (uint16_t)(end))))
#define CLEAR_16BITS(start, end) ~SET_16BITS(start, end)
#define GET_16BITS(val, start, end) (((val) & SET_16BITS((start), (end))) >> (start))

#define SET_32BIT(x) ((uint32_t)((uint32_t)0x01 << (x)))
#define CLEAR_32BIT(x) ~SET_32BIT(x)
#define SET_32BITS(start, end) ((0xFFFFFFFFUL << (start)) & (0xFFFFFFFFUL >> (31U - (uint32_t)(end))))
#define CLEAR_32BITS(start, end) ~SET_32BITS(start, end)
#define GET_32BITS(val, start, end) (((val) & SET_32BITS((start), (end))) >> (start))

/**
 * @brief transform little endian data to local 16bit unsigned data
 */
static inline uint16_t le_to_host16u(const uint8_t *buf) {
    uint16_t data = (uint16_t)buf[0] | ((uint16_t)buf[1] << 8);
    return data;
}

/**
 * @brief transform little endian data to local 16bit signed data
 */
static inline int16_t le_to_host16s(const uint8_t *buf) {
    return (int16_t)le_to_host16u(buf);
}

/**
 * @brief transform little endian data to local 32bit unsigned data
 */
static inline uint32_t le_to_host32u(const uint8_t *buf) {
    uint32_t data = (uint32_t)buf[0] | ((uint32_t)buf[1] << 8) | ((uint32_t)buf[2] << 16) | ((uint32_t)buf[3] << 24);
    return data;
}

/**
 * @brief transform little endian data to local 32bit signed data
 */
static inline int32_t le_to_host32s(const uint8_t *buf) {
    return (int32_t)le_to_host32u(buf);
}

/**
 * @brief transform big endian data to local 16bit unsigned data
 */
static inline uint16_t be_to_host16u(const uint8_t *buf) {
    uint16_t data = (uint16_t)buf[1] | ((uint16_t)buf[0] << 8);
    return data;
}

/**
 * @brief transform big endian data to local 16bit signed data
 */
static inline int16_t be_to_host16s(const uint8_t *buf) {
    return (int16_t)be_to_host16u(buf);
}

/**
 * @brief transform big endian data to local 32bit unsigned data
 */
static inline uint32_t be_to_host32u(const uint8_t *buf) {
    uint32_t data = (uint32_t)buf[3] | ((uint32_t)buf[2] << 8) | ((uint32_t)buf[1] << 16) | ((uint32_t)buf[0] << 24);
    return data;
}

/**
 * @brief transform big endian data to local 32bit signed data
 */
static inline int32_t be_to_host32s(const uint8_t *buf) {
    return (int32_t)be_to_host32u(buf);
}

/**
 * @brief transform local 16bit unsigned data to little endian buffer
 */
static uint8_t *host16u_to_le(uint16_t val, uint8_t *buf) {
    buf[0] = (uint8_t)val;
    buf[1] = (uint8_t)(val >> 8);
    return &buf[2];
}

/**
 * @brief transform local 16bit signed data to little endian buffer
 */
static uint8_t *host16s_to_le(int16_t val, uint8_t *buf) {
    return host16u_to_le((uint16_t)val, buf);
}

/**
 * @brief transform local 32bit unsigned data to little endian buffer
 */
static uint8_t *host32u_to_le(uint32_t val, uint8_t *buf) {
    buf[0] = (uint8_t)val;
    buf[1] = (uint8_t)(val >> 8);
    buf[2] = (uint8_t)(val >> 16);
    buf[3] = (uint8_t)(val >> 24);
    return &buf[4];
}

/**
 * @brief transform local 32bit signed data to little endian buffer
 */
static uint8_t *host32s_to_le(int32_t val, uint8_t *buf) {
    return host32u_to_le((uint32_t)val, buf);
}

/**
 * @brief transform local 16bit unsigned data to big endian buffer
 */
static uint8_t *host16u_to_be(uint16_t val, uint8_t *buf) {
    buf[0] = (uint8_t)(val >> 8);
    buf[1] = (uint8_t)val;
    return &buf[2];
}

/**
 * @brief transform local 16bit signed data to big endian buffer
 */
static uint8_t *host16s_to_be(int16_t val, uint8_t *buf) {
    return host16u_to_be((uint16_t)val, buf);
}

/**
 * @brief transform local 32bit unsigned data to big endian buffer
 */
static uint8_t *host32u_to_be(uint32_t val, uint8_t *buf) {
    buf[0] = (uint8_t)(val >> 24);
    buf[1] = (uint8_t)(val >> 16);
    buf[2] = (uint8_t)(val >> 8);
    buf[3] = (uint8_t)val;
    return &buf[4];
}

/**
 * @brief transform local 32bit signed data to big endian buffer
 */
static uint8_t *host32s_to_be(int32_t val, uint8_t *buf) {
    return host32u_to_be((uint32_t)val, buf);
}

#ifdef __cplusplus
}
#endif  // __cplusplus

#endif  // CONVERTER_H__
