{"name": "dTof_redLight", "target": "A5_RLT", "toolchain": "AC5", "toolchainLocation": "D:\\Programs\\keil_v5\\ARM\\ARMCC", "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.13\\res\\data\\models/arm.v5.model.json", "buildMode": "fast|multhread", "showRepathOnLog": true, "threadNum": 8, "rootDir": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "dumpPath": "build\\A5_RLT", "outDir": "build\\A5_RLT", "ram": 16384, "rom": 65536, "incDirs": [".", "../firmware/CMSIS/device/startup", "../firmware/CMSIS/device", "../firmware/CMSIS/core", "../firmware/n32g401_std_periph_driver/inc", ".cmsis/include", "RTE/_APP", "../bsp/inc", "../utils/inc", "app/cmd_handle", "app/uartfifo", "app/work_mode", "app/inc", "VI4302 API/BIN", "VI4302 API/inc", "user/inc"], "libDirs": [], "defines": ["N32G401", "USE_STDPERIPH_DRIVER"], "sourceList": ["../bsp/src/GPIO.c", "../bsp/src/adc.c", "../bsp/src/bsp_freq.c", "../bsp/src/delay.c", "../bsp/src/flash.c", "../bsp/src/spi.c", "../bsp/src/timer.c", "../bsp/src/usart.c", "../bsp/src/wdt.c", "../firmware/CMSIS/device/startup/startup_n32g401.s", "../firmware/CMSIS/device/system_n32g401.c", "../firmware/n32g401_std_periph_driver/src/misc.c", "../firmware/n32g401_std_periph_driver/src/n32g401_adc.c", "../firmware/n32g401_std_periph_driver/src/n32g401_beeper.c", "../firmware/n32g401_std_periph_driver/src/n32g401_comp.c", "../firmware/n32g401_std_periph_driver/src/n32g401_crc.c", "../firmware/n32g401_std_periph_driver/src/n32g401_dbg.c", "../firmware/n32g401_std_periph_driver/src/n32g401_dma.c", "../firmware/n32g401_std_periph_driver/src/n32g401_exti.c", "../firmware/n32g401_std_periph_driver/src/n32g401_flash.c", "../firmware/n32g401_std_periph_driver/src/n32g401_gpio.c", "../firmware/n32g401_std_periph_driver/src/n32g401_i2c.c", "../firmware/n32g401_std_periph_driver/src/n32g401_iwdg.c", "../firmware/n32g401_std_periph_driver/src/n32g401_lptim.c", "../firmware/n32g401_std_periph_driver/src/n32g401_pwr.c", "../firmware/n32g401_std_periph_driver/src/n32g401_rcc.c", "../firmware/n32g401_std_periph_driver/src/n32g401_rtc.c", "../firmware/n32g401_std_periph_driver/src/n32g401_spi.c", "../firmware/n32g401_std_periph_driver/src/n32g401_tim.c", "../firmware/n32g401_std_periph_driver/src/n32g401_usart.c", "../firmware/n32g401_std_periph_driver/src/n32g401_wwdg.c", "../utils/src/pid_ctrl.c", "../utils/src/proj_version.c", "../utils/src/protocol.c", "../utils/src/ring_buff.c", "../utils/src/task_process.c", "../utils/src/time_tasks.c", "VI4302 API/BIN/fw_44_00_00_80_R00.c", "VI4302 API/src/A2_Configurable.c", "VI4302 API/src/User_Driver.c", "VI4302 API/src/VI4302_Config.c", "VI4302 API/src/VI4302_Handle.c", "VI4302 API/src/VI4302_System.c", "VI4302 API/src/data_handle.c", "app/src/communication.c", "user/src/calibration_process.c", "user/src/device.c", "user/src/facula_process.c", "user/src/histogram_process.c", "user/src/main.c", "user/src/n32g401_it.c", "user/src/range_process.c", "user/src/scan_process.c", "user/src/tasks_polling.c", "user/src/variable_table.c"], "alwaysInBuildSources": [], "sourceParams": {}, "options": {"version": 4, "beforeBuildTasks": [{"name": "clean target file", "disable": false, "abortAfterFailed": false, "stopBuildAfterFailed": true, "command": "python ../tools/EE_clean_target_file.py --target ${ConfigName} --project ${ProjectName}"}, {"name": "get version from log", "disable": false, "abortAfterFailed": false, "stopBuildAfterFailed": true, "command": "python ../tools/EE_get_version_from_log.py ${ConfigName} ${ProjectName}"}], "afterBuildTasks": [{"name": "copy and rename hex,bin file to version name", "disable": false, "abortAfterFailed": false, "command": "python ../tools/EE_rename_hex_name.py --target ${ConfigName} --project ${ProjectName}"}, {"name": "generate elf file", "disable": false, "abortAfterFailed": false, "command": "axf2elf -d \"${ToolchainRoot}\" -i \"${outDir}\\${ProjectName}.axf\" -o \"${workspaceFolder}\\${OutDirRoot}\\${ProjectName}.elf\""}, {"name": "axf to elf", "command": "axf2elf -d \"${ToolchainRoot}\" -i \"${OutDir}/${ProjectName}.axf\" -o \"${OutDir}/${ProjectName}.elf\" > \"${OutDir}/axf2elf.log\""}], "global": {"use-microLIB": true, "output-debug-info": "enable", "microcontroller-cpu": "cortex-m4-sp", "microcontroller-fpu": "cortex-m4-sp", "microcontroller-float": "cortex-m4-sp", "$arch-extensions": "", "$clang-arch-extensions": "", "$armlink-arch-extensions": ""}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "ro-base": "0x00000000", "rw-base": "0x20000000", "link-scatter": ["f:/13_Ya<PERSON>-Laser-DTof2dMS/development/firmware/yapha_redLight_fw/projects/build/A5_RLT/dTof_redLight.sct"]}}, "env": {"workspaceFolder": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "workspaceFolderBasename": "projects", "OutDir": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\build\\A5_RLT", "OutDirRoot": "build", "OutDirBase": "build\\A5_RLT", "ProjectName": "dTof_redLight", "ConfigName": "A5_RLT", "ProjectRoot": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects", "ExecutableName": "f:\\13_Yapha-Laser-DTof2dMS\\development\\firmware\\yapha_redLight_fw\\projects\\build\\A5_RLT\\dTof_redLight", "ChipPackDir": ".pack/Nations/N32G401_DFP.1.1.0", "ChipName": "N32G401K8Q7-2", "SYS_Platform": "win32", "SYS_DirSep": "\\", "SYS_DirSeparator": "\\", "SYS_PathSep": ";", "SYS_PathSeparator": ";", "SYS_EOL": "\r\n", "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.13\\res\\tools\\win32\\unify_builder", "EIDE_BINARIES_VER": "12.1.1", "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin", "EIDE_PY3_CMD": "C:\\Users\\<USER>\\.eide\\bin\\python36\\python3.exe", "ToolchainRoot": "D:\\Programs\\keil_v5\\ARM\\ARMCC"}, "sysPaths": []}