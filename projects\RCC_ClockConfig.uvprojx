<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>N32G401</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>N32G401K8Q7-2</Device>
          <Vendor>Nationstech</Vendor>
          <PackID>Nationstech.N32G401_DFP.1.1.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x4000) IROM(0x08000000,0x10000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0N32G401 -********** -FL010000 -FP0($$Device:N32G401K8Q7-2$Flash\N32G401.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:N32G401K8Q7-2$firmware\CMSIS\device\n32g401.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:N32G401K8Q7-2$svd\N32G401.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>RCC_ClockConfig</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf --bin --output=.\bin\RCC_ClockConfig.bin .\Objects\RCC_ClockConfig.axf</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x4000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x10000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x4000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>1</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>N32G401, USE_STDPERIPH_DRIVER</Define>
              <Undefine></Undefine>
              <IncludePath>.\inc;..\projects;..\firmware\CMSIS\device\startup;..\firmware\CMSIS\device;..\firmware\CMSIS\core;..\firmware\n32g401_std_periph_driver\inc;..\BSP;..\BSP\cmd_handle;..\BSP\flash;..\BSP\uartfifo;..\BSP\VI4302 API\BIN;..\BSP\VI4302 API\inc;..\BSP\app;..\BSP\work_mode</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>USER</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\main.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\n32g401_it.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>BSP</GroupName>
          <Files>
            <File>
              <FileName>GPIO.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\GPIO.c</FilePath>
            </File>
            <File>
              <FileName>USART.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\USART.c</FilePath>
            </File>
            <File>
              <FileName>flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\flash\flash.c</FilePath>
            </File>
            <File>
              <FileName>uartfifo.C</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\uartfifo\uartfifo.C</FilePath>
            </File>
            <File>
              <FileName>adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\adc.c</FilePath>
            </File>
            <File>
              <FileName>cmd_handle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\cmd_handle\cmd_handle.c</FilePath>
            </File>
            <File>
              <FileName>delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\delay.c</FilePath>
            </File>
            <File>
              <FileName>spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\spi.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\timer.c</FilePath>
            </File>
            <File>
              <FileName>wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\wdt.c</FilePath>
            </File>
            <File>
              <FileName>converter.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\BSP\app\converter.h</FilePath>
            </File>
            <File>
              <FileName>work_mode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\work_mode\work_mode.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>VI4302</GroupName>
          <Files>
            <File>
              <FileName>data_handle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\data_handle.c</FilePath>
            </File>
            <File>
              <FileName>User_Driver.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\User_Driver.c</FilePath>
            </File>
            <File>
              <FileName>VI4302_Config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\VI4302_Config.c</FilePath>
            </File>
            <File>
              <FileName>VI4302_Handle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\VI4302_Handle.c</FilePath>
            </File>
            <File>
              <FileName>VI4302_System.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\VI4302_System.c</FilePath>
            </File>
            <File>
              <FileName>A2_Configurable.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\A2_Configurable.c</FilePath>
            </File>
            <File>
              <FileName>fw_44_00_00_80_R00.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\BIN\fw_44_00_00_80_R00.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FWLB</GroupName>
          <Files>
            <File>
              <FileName>misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\misc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_adc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_beeper.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_beeper.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_comp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_comp.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_crc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_dbg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_dbg.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_dma.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_exti.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_flash.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_gpio.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_i2c.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_lptim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_lptim.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_pwr.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_rcc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_rtc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_spi.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_tim.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_usart.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_wwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_wwdg.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_n32g401.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\CMSIS\device\system_n32g401.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>STARTUP</GroupName>
          <Files>
            <File>
              <FileName>startup_n32g401.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\firmware\CMSIS\device\startup\startup_n32g401.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Read_Me</GroupName>
          <Files>
            <File>
              <FileName>Read_Me.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\Read_Me\Read_Me.txt</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>APP</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>N32G401K8Q7-2</Device>
          <Vendor>Nationstech</Vendor>
          <PackID>Nationstech.N32G401_DFP.1.1.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x4000) IROM(0x08000000,0x10000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0N32G401 -********** -FL010000 -FP0($$Device:N32G401K8Q7-2$Flash\N32G401.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:N32G401K8Q7-2$firmware\CMSIS\device\n32g401.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:N32G401K8Q7-2$svd\N32G401.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>VM4314_APP_V10</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf --bin --output=.\bin\RCC_ClockConfig.bin .\Objects\RCC_ClockConfig.axf</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x4000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x10000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8004000</StartAddress>
                <Size>0xc000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x4000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>N32G401, USE_STDPERIPH_DRIVER,BOOT_APP,WDT_DEF</Define>
              <Undefine></Undefine>
              <IncludePath>.\inc;..\projects;..\firmware\CMSIS\device\startup;..\firmware\CMSIS\device;..\firmware\CMSIS\core;..\firmware\n32g401_std_periph_driver\inc;..\BSP;..\BSP\cmd_handle;..\BSP\flash;..\BSP\uartfifo;..\BSP\VI4302 API\BIN;..\BSP\VI4302 API\inc</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>USER</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\main.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\n32g401_it.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>BSP</GroupName>
          <Files>
            <File>
              <FileName>GPIO.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\GPIO.c</FilePath>
            </File>
            <File>
              <FileName>USART.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\USART.c</FilePath>
            </File>
            <File>
              <FileName>flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\flash\flash.c</FilePath>
            </File>
            <File>
              <FileName>uartfifo.C</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\uartfifo\uartfifo.C</FilePath>
            </File>
            <File>
              <FileName>adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\adc.c</FilePath>
            </File>
            <File>
              <FileName>cmd_handle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\cmd_handle\cmd_handle.c</FilePath>
            </File>
            <File>
              <FileName>delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\delay.c</FilePath>
            </File>
            <File>
              <FileName>spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\spi.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\timer.c</FilePath>
            </File>
            <File>
              <FileName>wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\wdt.c</FilePath>
            </File>
            <File>
              <FileName>converter.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\BSP\app\converter.h</FilePath>
            </File>
            <File>
              <FileName>work_mode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\work_mode\work_mode.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>VI4302</GroupName>
          <Files>
            <File>
              <FileName>data_handle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\data_handle.c</FilePath>
            </File>
            <File>
              <FileName>User_Driver.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\User_Driver.c</FilePath>
            </File>
            <File>
              <FileName>VI4302_Config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\VI4302_Config.c</FilePath>
            </File>
            <File>
              <FileName>VI4302_Handle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\VI4302_Handle.c</FilePath>
            </File>
            <File>
              <FileName>VI4302_System.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\VI4302_System.c</FilePath>
            </File>
            <File>
              <FileName>A2_Configurable.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\A2_Configurable.c</FilePath>
            </File>
            <File>
              <FileName>fw_44_00_00_80_R00.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\BIN\fw_44_00_00_80_R00.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FWLB</GroupName>
          <Files>
            <File>
              <FileName>misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\misc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_adc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_beeper.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_beeper.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_comp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_comp.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_crc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_dbg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_dbg.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_dma.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_exti.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_flash.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_gpio.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_i2c.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_lptim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_lptim.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_pwr.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_rcc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_rtc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_spi.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_tim.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_usart.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_wwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_wwdg.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_n32g401.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\CMSIS\device\system_n32g401.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>STARTUP</GroupName>
          <Files>
            <File>
              <FileName>startup_n32g401.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\firmware\CMSIS\device\startup\startup_n32g401.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Read_Me</GroupName>
          <Files>
            <File>
              <FileName>Read_Me.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\Read_Me\Read_Me.txt</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>APP_NO_WDT</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pArmCC>5060750::V5.06 update 6 (build 750)::ARMCC</pArmCC>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>N32G401K8Q7-2</Device>
          <Vendor>Nationstech</Vendor>
          <PackID>Nationstech.N32G401_DFP.1.1.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x4000) IROM(0x08000000,0x10000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0N32G401 -********** -FL010000 -FP0($$Device:N32G401K8Q7-2$Flash\N32G401.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:N32G401K8Q7-2$firmware\CMSIS\device\n32g401.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:N32G401K8Q7-2$svd\N32G401.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>VM4314_APP_V10</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf --bin --output=.\bin\RCC_ClockConfig.bin .\Objects\RCC_ClockConfig.axf</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x4000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x10000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8004000</StartAddress>
                <Size>0xc000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x4000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>N32G401, USE_STDPERIPH_DRIVER,BOOT_APP</Define>
              <Undefine></Undefine>
              <IncludePath>.\inc;..\projects;..\firmware\CMSIS\device\startup;..\firmware\CMSIS\device;..\firmware\CMSIS\core;..\firmware\n32g401_std_periph_driver\inc;..\BSP;..\BSP\cmd_handle;..\BSP\flash;..\BSP\uartfifo;..\BSP\VI4302 API\BIN;..\BSP\VI4302 API\inc</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>USER</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\main.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\src\n32g401_it.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>BSP</GroupName>
          <Files>
            <File>
              <FileName>GPIO.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\GPIO.c</FilePath>
            </File>
            <File>
              <FileName>USART.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\USART.c</FilePath>
            </File>
            <File>
              <FileName>flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\flash\flash.c</FilePath>
            </File>
            <File>
              <FileName>uartfifo.C</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\uartfifo\uartfifo.C</FilePath>
            </File>
            <File>
              <FileName>adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\adc.c</FilePath>
            </File>
            <File>
              <FileName>cmd_handle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\cmd_handle\cmd_handle.c</FilePath>
            </File>
            <File>
              <FileName>delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\delay.c</FilePath>
            </File>
            <File>
              <FileName>spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\spi.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\timer.c</FilePath>
            </File>
            <File>
              <FileName>wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\wdt.c</FilePath>
            </File>
            <File>
              <FileName>converter.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\BSP\app\converter.h</FilePath>
            </File>
            <File>
              <FileName>work_mode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\work_mode\work_mode.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>VI4302</GroupName>
          <Files>
            <File>
              <FileName>data_handle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\data_handle.c</FilePath>
            </File>
            <File>
              <FileName>User_Driver.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\User_Driver.c</FilePath>
            </File>
            <File>
              <FileName>VI4302_Config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\VI4302_Config.c</FilePath>
            </File>
            <File>
              <FileName>VI4302_Handle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\VI4302_Handle.c</FilePath>
            </File>
            <File>
              <FileName>VI4302_System.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\VI4302_System.c</FilePath>
            </File>
            <File>
              <FileName>A2_Configurable.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\src\A2_Configurable.c</FilePath>
            </File>
            <File>
              <FileName>fw_44_00_00_80_R00.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\VI4302 API\BIN\fw_44_00_00_80_R00.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FWLB</GroupName>
          <Files>
            <File>
              <FileName>misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\misc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_adc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_beeper.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_beeper.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_comp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_comp.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_crc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_dbg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_dbg.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_dma.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_exti.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_flash.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_gpio.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_i2c.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_lptim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_lptim.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_pwr.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_rcc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_rtc.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_spi.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_tim.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_usart.c</FilePath>
            </File>
            <File>
              <FileName>n32g401_wwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\n32g401_std_periph_driver\src\n32g401_wwdg.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_n32g401.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\firmware\CMSIS\device\system_n32g401.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>STARTUP</GroupName>
          <Files>
            <File>
              <FileName>startup_n32g401.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\firmware\CMSIS\device\startup\startup_n32g401.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Read_Me</GroupName>
          <Files>
            <File>
              <FileName>Read_Me.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\Read_Me\Read_Me.txt</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>&lt;Project Info&gt;</LayName>
        <LayDesc></LayDesc>
        <LayUrl></LayUrl>
        <LayKeys></LayKeys>
        <LayCat></LayCat>
        <LayLic></LayLic>
        <LayTarg>0</LayTarg>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
