/*
 * @Author: xx
 * @Date: 2024-07-31 16:37:38
 * @LastEditors: Do not edit
 * @LastEditTime: 2024-08-05 19:30:44
 * @Description:
 * @FilePath: \MDK-ARMc:\Users\<USER>\Desktop\fw01_dToF_lidar\App\src\pid_ctrl.c
 */
#include "pid_ctrl.h"

pid_speed_t g_pid_speed;

void pid_param_init(pid_speed_t *pid, uint16_t spd) {
    float spd_f = 180.0f;
    if (spd >= 30 && spd <= 160) {
        spd_f = 1.8f * spd;
    }
    pid->speed_set      = spd_f;
    pid->speed_feedback = 0;
    pid->integral       = 0;
    pid->err_last       = 0;
    pid->kp             = 4.0f;  // 1.2f;
    pid->ki             = 1.2f;  // 0.5f;
    pid->i_limit        = 5000;
    pid->ctrl_out       = 0;

    pid->speed_sum_cur           = 0;
    pid->speed_sum_last          = 0;
    pid->speed_interval          = 0;
    pid->speed_interval_cnt      = 0;
    pid->speed_interval_cnt_last = 0;
    pid->speed_duty_last         = 0;
}

void pid_calc_loop(pid_speed_t *pid, bool is_running) {
    if (is_running) {
        float err     = pid->speed_set - pid->speed_feedback;
        pid->err_last = err;

        if (fabs(err) < 0.36f) {
            err = 0;
        }

        pid->integral += err;
        if (pid->integral > pid->i_limit) {
            pid->integral = pid->i_limit;
        }
        // else if (pid->integral < 20) {
        //     pid->integral = 20;
        // }

        // 计算输出值
        pid->ctrl_out = pid->kp * err + pid->ki * pid->integral;

        if (pid->ctrl_out > 10000) {
            pid->ctrl_out = 10000;
        }
    } else {
        pid->integral = 1000;
        pid->ctrl_out = 0;
    }
}