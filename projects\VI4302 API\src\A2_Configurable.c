#include "A2_Configurable.h"
#include "VI4302_System.h"
#include "main.h"
//#include "User_Driver.h"
//#include "SPI.h"
//#include "systick.h"


#if     0
//可配置参数全部写入（内部验证）
uint8_t A2_Configurable_Init(A2_Configurable_Parameters *Conf_Para_struct)
{
	uint8_t status = 0U;
	Spi_Write_Reg ( 0x31, Conf_Para_struct->parameter_types&0XFF);
	Spi_Write_Reg ( 0x32, (Conf_Para_struct->parameter_types>>8)&0XFF);
	Spi_Write_Reg ( 0x33, Conf_Para_struct->para_S);
	Spi_Write_Reg ( 0x34, Conf_Para_struct->para_D);
	Spi_Write_Reg ( 0x35, Conf_Para_struct->para_MP);
	Spi_Write_Reg ( 0x36, Conf_Para_struct->xtalk_bin&0xff);
	Spi_Write_Reg ( 0x37, (Conf_Para_struct->xtalk_bin>>8)&0xff);
	Spi_Write_Reg ( 0x38, Conf_Para_struct->xtalk_peak&0xff);
	Spi_Write_Reg ( 0x39, (Conf_Para_struct->xtalk_peak>>8)&0xff);
	Spi_Write_Reg ( 0x3A, Conf_Para_struct->flag_1_gap);
	Spi_Write_Reg ( 0x3B, Conf_Para_struct->flag_1_peak_ratio);
	Spi_Write_Reg ( 0x3C, Conf_Para_struct->xtalk_rang_cnt);
	Spi_Write_Reg ( 0x3D, Conf_Para_struct->confidence_k);
	Spi_Write_Reg ( 0x3E, Conf_Para_struct->flag_2_gap);
	Spi_Write_Reg ( 0x3F, Conf_Para_struct->confidence_mode);
	Spi_Send_Cmd ( CONF_PARA_CONFIG_CMD );
	status =  vi4302_read_status_from_firmware ( FIRMWARE_MAX_REPLY_TIME);
	return status;
}

//可配置参数全部读取（内部验证）
uint8_t A2_Configurable_Parameters_Read(uint8_t *Rx_Data)
{
	uint8_t status = 0U;
	Spi_Send_Cmd ( CONF_PARA_READ_CMD );
	status = vi4302_read_status_from_firmware ( FIRMWARE_MAX_REPLY_TIME);
	if(status == FIRMWARE_STATUS_ACK)
		{
			Rx_Data[0]=Spi_Read_Reg ( 0x33 );//S
			Rx_Data[1]=Spi_Read_Reg ( 0x34 );//D
			Rx_Data[2]=Spi_Read_Reg ( 0x35 );//MP
			Rx_Data[3]=Spi_Read_Reg ( 0x36 );//XTALK_BIN
			Rx_Data[4]=Spi_Read_Reg ( 0x37 );//XTALK_PEAK_L
			Rx_Data[5]=Spi_Read_Reg ( 0x38 );//XTALK_PEAK_H
			Rx_Data[6]=Spi_Read_Reg ( 0x39 );//OFFSET
			Rx_Data[7]=Spi_Read_Reg ( 0x3A );
			Rx_Data[8]=Spi_Read_Reg ( 0x3B );
			Rx_Data[9]=Spi_Read_Reg ( 0x3C );
			Rx_Data[10]=Spi_Read_Reg ( 0x3D );
			Rx_Data[11]=Spi_Read_Reg ( 0x3E );
			Rx_Data[12]=Spi_Read_Reg ( 0x3F );
		}
	return status;
}

//配置S参数的使能和失能【可在Xtalk标定前调用】
//输入参数： rang_cnt  
//返回值：0X11 成功  S_PARAMETER
uint8_t S_parameter_init(uint8_t s_para)
{ 
	uint8_t status = 0U;
	uint8_t s_para_write=0;
	if(s_para!=0)
	{
	  s_para_write=1;
	}
	uint16_t parameter_types = S_PARAMETER;
	Spi_Write_Reg ( 0x31, parameter_types&0xff);
	Spi_Write_Reg ( 0x32, (parameter_types>>8)&0xff); 
	Spi_Write_Reg ( 0x33, s_para_write);
	Spi_Send_Cmd ( 0X43);
	status = vi4302_read_status_from_firmware ( FIRMWARE_MAX_REPLY_TIME);
	return status;
}

//配置D参数的使能和失能【可在Xtalk标定前调用】
//输入参数： uint8_t D_para
//返回值：0X11 成功  
uint8_t D_parameter_init(uint8_t D_para)
{ 
	uint8_t status = 0U;
	uint8_t D_para_write=0;
	if(D_para!=0)
	{
	  D_para_write=1;
	}
	uint16_t parameter_types = D_PARAMETER;
	Spi_Write_Reg ( 0x31, parameter_types&0xff);
	Spi_Write_Reg ( 0x32, (parameter_types>>8)&0xff); 
	Spi_Write_Reg ( 0x34, D_para_write);
	Spi_Send_Cmd ( 0X43);
	status = vi4302_read_status_from_firmware ( FIRMWARE_MAX_REPLY_TIME);
	return status;
}

//配置MP num
//输入参数： uint8_t MP_cnt  
//返回值：0X11 成功  
uint8_t MP_cnt_init(uint8_t MP_cnt)
{ 
	uint8_t status = 0U;
	uint16_t parameter_types = MP_PARAMETER;
	Spi_Write_Reg ( 0x31, parameter_types&0xff);
	Spi_Write_Reg ( 0x32, (parameter_types>>8)&0xff); 
	Spi_Write_Reg ( 0x35, MP_cnt);
	Spi_Send_Cmd ( 0X43);
	status = vi4302_read_status_from_firmware ( FIRMWARE_MAX_REPLY_TIME);
	return status;
}

/*******************************************************************************
 *将此函数置于寄存器配置结束、帧率配置之前
 *xtalk_bin :观察 normal tof直方图，记录罩子成峰对应的bin1，切换为reference tof直方图，记录bin2，该值为bin1 - bin2的差
 *xtalk_peak：xtalk peak
 *******************************************************************************/
/*******************************************************************************
 * Function Name  : Xtalk_Para_Init.
 * Description    : Xtalk的配置
 * Output         : None.
 * Return         : uint8_t 0 timeout  0x11 success  0x12 fail
 *******************************************************************************/
uint8_t Xtalk_Para_Init(uint16_t xtalk_bin,uint16_t xtalk_peak)
{
	uint8_t status = 0U;
	uint16_t parameter_types = XTALK_BIN|XTALK_PEAK;
	Spi_Write_Reg ( 0x31, parameter_types&0xff);
	Spi_Write_Reg ( 0x32, (parameter_types>>8)&0xff); 
	Spi_Write_Reg ( 0x36, xtalk_bin);
	Spi_Write_Reg ( 0x37, (xtalk_bin>>8)&0xff);
	Spi_Write_Reg ( 0x38, xtalk_peak&0xff);
	Spi_Write_Reg ( 0x39, (xtalk_peak>>8)&0xff);
	Spi_Send_Cmd (0X43);
	status = vi4302_read_status_from_firmware ( FIRMWARE_MAX_REPLY_TIME);
	return status;
}

//配置xtalk标定的测距次数【可在Xtalk标定前调用】
//输入参数： rang_cnt  
//返回值：0X11 成功  
uint8_t xtalk_rang_cnt_init(uint8_t rang_cnt)
{
	uint8_t status = 0U;
	uint16_t parameter_types = XTALK_RANG_CNT;
	Spi_Write_Reg ( 0x31, parameter_types&0xff);
	Spi_Write_Reg ( 0x32, (parameter_types>>8)&0xff); 
	Spi_Write_Reg ( 0x3C, rang_cnt);
	Spi_Send_Cmd ( 0X43);
	status = vi4302_read_status_from_firmware ( FIRMWARE_MAX_REPLY_TIME);
	return status;
}

//灰尘检测参数，检出峰的bin位于xtalk峰附近，peak满足条件
//输入参数： 
	//gap1: 得出罩子脏污这个结论前（flag = 1），判定检出峰的bin位于xtalk峰附近时，冗余空间 15 >= gap1 >=5 
	//gap2：得出罩子极度脏污或紧贴罩子存在真实目标物这个结论前（flag = 2），判定检出峰的bin位于xtalk峰附近时，冗余空间 12 >= gap2 >=2 
	//raito：判定（flag = 1）的peak限制条件， peak > raito*xtalk_peak (0<raito<=10)
//返回值：0X11 成功 
uint8_t Dust_detection_init(uint8_t gap1,uint8_t gap2,uint8_t raito)
{
	uint8_t status = 0U;
	uint16_t parameter_types = GAP_1|GAP_2|RAITO;
	Spi_Write_Reg ( 0x31, parameter_types&0xff);
	Spi_Write_Reg ( 0x32, (parameter_types>>8)&0xff); 
	Spi_Write_Reg ( 0x3A, gap1);
	Spi_Write_Reg ( 0x3B, raito);
	Spi_Write_Reg ( 0x3E, gap2);
	Spi_Send_Cmd ( 0X43);
 status = vi4302_read_status_from_firmware ( FIRMWARE_MAX_REPLY_TIME);
 return status;
}

//配置置信度参数K，提高弱反射率直射  或  强反射率板大角度回波信号的置信度[仅建议在出峰逻辑为模式0的情况下使用]
//输入参数： 0-64   值越小测到弱反射率板的可能性越大但是太小了以后噪声也会很高，从而导致异常
//返回值：0X11 成功  
//Dust detection
uint8_t confidence_k_init(uint8_t conf_k)
{
	uint8_t status = 0U;
	uint16_t parameter_types = CONFIDENCE_K;
	Spi_Write_Reg ( 0x31, parameter_types&0xff);
	Spi_Write_Reg ( 0x32, (parameter_types>>8)&0xff); 
	Spi_Write_Reg ( 0x3D, conf_k);
	Spi_Send_Cmd ( 0X43);
	status = vi4302_read_status_from_firmware ( FIRMWARE_MAX_REPLY_TIME);
	return status;
}

//xtalk标定命令
//输入参数：
		//	bin_num  直方图截断
		//	*X_bin*X_peak 存储标定后的值，存入flash，再次上电后读取写入即可
		//  retry_cnt下发标定指令后等待GPIO_0中断的时长，做超时判断
//返回值：0X11 成功 
uint8_t xtalk_cal(uint8_t bin_num,uint16_t *X_bin,uint16_t *X_peak,uint32_t retry_cnt)
{
	uint8_t status = 0U;
	
	gpio0_int_cnt = 0;
	Spi_Write_Reg ( 0x31, bin_num&0xff);
    Spi_Write_Reg ( 0x32, (bin_num>>8)&0xff);
	Spi_Send_Cmd (XTALK_CAL_CMD);	

	while ( retry_cnt != 0 )
    {
        retry_cnt--;
				if(gpio0_int_cnt > 0)
					{
					  status = Spi_Read_Reg ( 0x30 );
						if(status == FIRMWARE_STATUS_ACK)
						{
						  	*X_bin = Spi_Read_Reg ( 0x31 )+( Spi_Read_Reg ( 0x32 ) <<8);
								*X_peak = Spi_Read_Reg ( 0x33 )+( Spi_Read_Reg ( 0x34 ) <<8);
							   break;
						}
						else
						{
							status = 0x88;//中断有但是应答值不对
							break;
						}
					}	
    }
	return status;
} 



//接收芯视界上位机指令 写入全部参数的接口
uint8_t A2_Configurable_Init_AF(A2_Configurable_Parameters *A2_Configurable_Init_struct_af)
{
	uint8_t status = 0U;
	status = A2_Configurable_Init(A2_Configurable_Init_struct_af);
	return status;
}
#endif

