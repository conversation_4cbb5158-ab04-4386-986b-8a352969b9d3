/**
 * @file device.h
 * <AUTHOR> name (<EMAIL>)
 * @brief 当前设备属性，包含 功能属性和通讯属性等等
 * @version 0.1
 * @date 2025-08-01
 *
 * @copyright Copyright (c) 2025
 *
 */

#ifndef _DEVICE_H_
#define _DEVICE_H_


#include "typedef.h"


/***************************COMMUNICATION*********/
/*模式与枚举（与D/T系列相同）*/
enum EModeData {
    kTriStopMode        = 0xA5FF,
    kTriTestMode        = 0xA5FE,
    kTriFaculaMode      = 0xA5FD,
    kTriScanMode        = 0xA5FC,
    kTriCalibrationMode = 0xA5FB,

    kTofStopMode        = 0xA5FF,
    kTofHistMode        = 0xA5FE,
    kTofFaculaMode      = 0xA5FD,
    kTofScanMode        = 0xA5FC,
    kTofCalibrationMode = 0xA5FB,
    kTofSinglePointMode = 0xA5FA,
    kTofRangingMode     = 0xA5F9,
    kTofRevCheckMode    = 0xA5F8,
    kTofSize
};

typedef enum {
    kTriMode          = 0xA0,
    kTofMode          = 0xA1,
    kSysParam         = 0xA2,
    kMcuId            = 0xA3,
    kVersionBuad      = 0xA4,
    kMeasureRange     = 0xA5,
    kCalibrationParam = 0xA6,
    kTgParam          = 0xA7,
    kReflelParam      = 0xA8,
    kHisAndFacula     = 0xA9,
    kTemperature      = 0xAA,
    kRegisterSetting  = 0xAB,
    kZeroAngleSetting = 0xAC,
    kLaserControl     = 0xAD,
    kLedControl       = 0xAE,
    kVbd              = 0xAF,
    kDebugSetting4    = 0xBC,
    kBootloader       = 0xBD,
    kDebugSetting2    = 0xBE,
    kDebugSetting     = 0xBF,
    kRangingMode      = 0xD0,
    kXtalkCalc        = 0xD1,

    //底板替换指令
    kBtMotorCtrl = 0xF0,  // kRunning  kStandby  kTurnOn  kTurnOff
    kBtMotorCode = 0xF1,
    kDdsInfo     = 0xF2,

    kSize,
} ECmdID;


/**
 * @brief: 设备接口函数
 */
typedef struct {
    void (*device_init)(void);
    void (*command_handle)(void);  //指令处理函数
    void (*device_info)(void);
    void (*device_begin_info)(void);
} API_Device_T;


extern const API_Device_T *const g_device_ptr;

#endif /* _DEVICE_H_ */
