#ifndef _TIME_TASKS_h_
#define _TIME_TASKS_h_

#include <stdbool.h>
#include <stdint.h>


typedef struct _TASK_COMPONENTS {
    uint8_t  Run;    //
    uint16_t Timer;  //
    uint8_t  priority;
    uint16_t ItvTime;  //

    void (*callback)(void);  //
    struct _TASK_COMPONENTS *next;
} TaskComponents;


typedef struct {
    void (*IntPtr)(uint16_t, uint16_t);
    void (*EnablePtr)(_Bool);
} PtrTimer;

typedef struct {
    bool (*register_tasks)(int, unsigned int, void (*callback)(void));
    void (*task_time_poll)(void);
    void (*task_poll)(void);
} API_TimeTask_T;

extern const API_TimeTask_T *const g_time_task_ptr;

#endif
