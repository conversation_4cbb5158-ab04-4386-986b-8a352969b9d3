<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.4.0" 
    xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
    <vendor>Nationstech</vendor>
    <url>http://www.keil.com/pack/</url>
    <name>N32G401_DFP</name>

    <description>Nationstech N32G401 Series Device Support, Drivers and Examples</description>

    <releases>
		<release version="1.0.0">
            Add The Device
        </release>
		<release version="1.1.0">
            Add N32G401K8Q7-1 and N32G401K8Q7-2
        </release>
    </releases>

    <keywords>
        <keyword>Nationstech</keyword>
        <keyword>Device Support</keyword>
        <keyword>N32G401</keyword>
        <keyword>N32</keyword>
    </keywords>

    <devices>
        <family Dfamily="N32G401 Series" Dvendor="Nationstech:928">
            <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="0" Dendian="Little-endian"/>
            <description>
            </description>

            <subFamily DsubFamily="N32G401">
                <description>
Up to 72MHz ARM Cortex-M4F, 64KB Flash, 8KB SRAM, 39+1xGPIO(MAX), 8xTimer, RTC, 22xPWM(MAX), 1x12bit 5Msps ADC, 4xU(S)ART, 3xCOMP, 2xSPI/I2S, 2xI2C, 1xDMA Hardware Cryptographic Engine.
                </description>
                <processor Dclock="72000000"/>
                <compile header="firmware/CMSIS/device/n32g401.h" define="N32G401 USE_STDPERIPH_DRIVER"/>
						
	
<device Dname="N32G401C8L7">
    <description>
&#xA;&#xD;
- Package:LQFP48&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  64KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    39+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 16 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   2&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x10000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x10000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>	

/*
<device Dname="N32G401C8Q7">
    <description>
&#xA;&#xD;
- Package:QFN48&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  64KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    39+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 16 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   2&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x10000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x10000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401K8L7">
    <description>
&#xA;&#xD;
- Package:LQFP32&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  64KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    25+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 10 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   2&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x10000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x10000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401K8Q7">
    <description>
&#xA;&#xD;
- Package:QFN32&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  64KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    25+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 10 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   2&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x10000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x10000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401K8Q7-1">
    <description>
&#xA;&#xD;
- Package:QFN32-1&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  64KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    27+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 11 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   2&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x10000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x10000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401K8Q7-2">
    <description>
&#xA;&#xD;
- Package:QFN32-2&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  64KB&#xA;&#xD;
- SRAM:   16KB&#xA;&#xD;
- I/O:    27+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 14 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   2&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x4000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x10000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x10000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401G8Q7">
    <description>
&#xA;&#xD;
- Package:QFN28&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  64KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    23+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 10 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   2&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x10000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x10000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401F8Q7">
    <description>
&#xA;&#xD;
- Package:QFN20&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  64KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    15+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 7 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   1&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x10000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x10000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401F8S7">
    <description>
&#xA;&#xD;
- Package:TSSOP20&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  64KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    15+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 9 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   1&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x10000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x10000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401F8S7-1">
    <description>
&#xA;&#xD;
- Package:TSSOP20&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  64KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    15+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 9 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   1&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x10000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x10000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401C6L7">
    <description>
&#xA;&#xD;
- Package:LQFP48&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  32KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    39+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 16 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   2&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x8000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x8000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>	

<device Dname="N32G401C6Q7">
    <description>
&#xA;&#xD;
- Package:QFN48&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  32KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    39+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 16 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   2&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x8000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x8000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401K6L7">
    <description>
&#xA;&#xD;
- Package:LQFP32&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  32KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    25+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 10 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   2&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x8000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x8000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401K6Q7">
    <description>
&#xA;&#xD;
- Package:QFN32&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  32KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    25+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 10 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   2&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x8000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x8000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401G6Q7">
    <description>
&#xA;&#xD;
- Package:QFN28&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  32KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    23+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 10 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   2&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x8000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x8000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401F6Q7">
    <description>
&#xA;&#xD;
- Package:QFN20&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  32KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    15+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 7 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   1&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x8000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x8000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401F6S7">
    <description>
&#xA;&#xD;
- Package:TSSOP20&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  32KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    15+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 9 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   1&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x8000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x8000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>

<device Dname="N32G401F6S7-1">
    <description>
&#xA;&#xD;
- Package:TSSOP20&#xA;&#xD;
- Clock:  72MHz&#xA;&#xD;
- Flash:  32KB&#xA;&#xD;
- SRAM:   8KB&#xA;&#xD;
- I/O:    15+1&#xA;&#xD;
- Voltage: 1.8V~3.6V -40~+105°C&#xA;&#xD;
- Timer:  8&#xA;&#xD;
- RTC:    1&#xA;&#xD;
- PWM:    22&#xA;&#xD;
- BEEPER: 1&#xA;&#xD;
- ADC:    1x12bit with 9 channels&#xA;&#xD;
- USART:  2&#xA;&#xD;
- UART:   1&#xA;&#xD;
- SPI/I2S:2/2&#xA;&#xD;
- I2C:    2&#xA;&#xD;
- DMA:    1*8 channels&#xA;&#xD;
- COMP:   3&#xA;&#xD;
- CRC16/CRC32
&#xA;&#xD;
    </description>
    <memory id="IRAM1" access="rw" start="0x20000000" size="0x2000" default="1" init="0" />
    <memory id="IROM1" access="rx" start="0x08000000" size="0x8000" default="1" startup="1" />
    <algorithm name="Flash/N32G401.FLM" start="0x08000000" size="0x8000" default="1" startup="1" />
    <debug svd="svd/N32G401.svd"/>
</device>
*/

            </subFamily>
        </family>
    </devices>
        

    <conditions>
        <condition id="Compiler ARM">            <!-- conditions selecting ARM Compiler -->
            <require Tcompiler="ARMCC"/>
        </condition>

        <condition id="N32G401 CMSIS Device">            <!-- conditions selecting Devices -->
            <description>Nationstech N32G401 Series devices</description>
            <require Cclass ="CMSIS" Cgroup="CORE" Csub=""/>
            <require Dvendor="Nationstech:928" Dname="N32*"/>
        </condition>

        <condition id="N32G401 STDPERIPH">
            <description>Nationstech N32G401 Standard Peripherals Drivers</description>
            <require condition="N32G401 CMSIS Device"/>
            <require Cclass="Device" Cgroup="StdPeriph Drivers" Csub="Framework"/>
        </condition>

        <condition id="N32G401 STDPERIPH RCC">
            <description>Nationstech N32G401 Standard Peripherals Drivers with RCC</description>
            <require condition="N32G401 STDPERIPH"/>
            <require Cclass="Device" Cgroup="StdPeriph Drivers" Csub="RCC"/>
        </condition>

        <condition id="N32G401 Algorithm Common" >
            <description>Nationstech N32G401 Series Algorithm Libraries Common Functions.</description>
            <require condition="N32G401 CMSIS Device"/>
            <require Cclass ="Device" Cgroup="Algorithm Libs" Csub="Common"/>
        </condition>

    </conditions>

    <components>
        <component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="N32G401 CMSIS Device">
            <description>Startup File for Nationstech N32G401 Series</description>
            <files>
                <!--  include folder -->
                <file category="include" name="firmware/CMSIS/device/"/>
                <file category="source" name="firmware/CMSIS/device/startup/startup_n32g401.s" attr="config" condition="N32G401 CMSIS Device" version="1.0.0"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="System_N32G401" Cversion="1.0.0" condition="N32G401 CMSIS Device">
            <description>System Clock Config File for Nationstech N32G401</description>
            <files>
                <!--  include folder -->
                <file category="include" name="firmware/CMSIS/device/"/>
                <file category="source" name="firmware/CMSIS/device/system_n32g401.c" attr="config" version="1.0.0"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="Framework" Cversion="1.0.0" condition="N32G401 STDPERIPH">
            <description>Standard Peripherals Drivers Framework</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_FRAMEWORK
            </RTE_Components_h>
            <files>
                <file category="include" name="firmware/n32g401_std_periph_driver/inc/"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/misc.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="ADC" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>Analog-to-digital converter (ADC) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_ADC
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_adc.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_adc.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="COMP" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>Comparator (COMP) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_COMP
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_comp.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_comp.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="CRC" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>CRC calculation unit (CRC) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_CRC
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_crc.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_crc.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="MCUDBG" Cversion="1.0.0" condition="N32G401 STDPERIPH">
            <description>MCU debug component (MCUDBG) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_DBG
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_dbg.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_dbg.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="DMA" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>DMA controller (DMA) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_DMA
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_dma.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_dma.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="EXTI" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>External interrupt/event controller (EXTI) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_EXTI
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_exti.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_exti.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="FLASH" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>Embedded Flash memory (FLASH) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_FLASH
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_flash.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_flash.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="GPIO" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>General-purpose I/O (GPIO) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_GPIO
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_gpio.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_gpio.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="I2C" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>Inter-integrated circuit (I2C) interface driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_I2C
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_i2c.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_i2c.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="IWDG" Cversion="1.0.0" condition="N32G401 STDPERIPH">
            <description>Independent watchdog (IWDG) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_IWDG
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_iwdg.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_iwdg.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="LPTIM" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>Low Power Timers (LPTIM) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_LPTIM
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_lptim.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_lptim.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="PWR" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>Power controller (PWR) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_PWR
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_pwr.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_pwr.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="RCC" Cversion="1.0.0" condition="N32G401 STDPERIPH">
            <description>Reset and clock control (RCC) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_RCC
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_rcc.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_rcc.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="RTC" Cversion="1.0.0" condition="N32G401 STDPERIPH">
            <description>Real-time clock (RTC) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_RTC
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_rtc.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_rtc.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="SPI" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>Serial peripheral interface (SPI) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_SPI
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_spi.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_spi.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="TIMER" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>Timers (TIMER) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_TIM
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_tim.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_tim.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="USART" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>Universal synchronous asynchronous receiver transmitter (USART) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_USART
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_usart.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_usart.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="WWDG" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>Window watchdog (WWDG) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_WWDG
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_wwdg.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_wwdg.c"/>
            </files>
        </component>
		
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="BEEPER" Cversion="1.0.0" condition="N32G401 STDPERIPH RCC">
            <description>Beeper(BEEPER) driver for N32G401</description>
            <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_BEEPER
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_std_periph_driver/inc/n32g401_beeper.h"/>
                <file category="source" name="firmware/n32g401_std_periph_driver/src/n32g401_beeper.c"/>
            </files>
        </component>

        <component Cclass="Device" Cgroup="Algorithm Libs" Csub="Common" Cversion="1.0.0" condition="N32G401 CMSIS Device">
            <RTE_Components_h>
        #define RTE_DEVICE_ALGO_COMMON
            </RTE_Components_h>
            <files>
                <file category="include" name="firmware/n32g401_algo_lib/inc" />
                <file category="header" name="firmware/n32g401_algo_lib/inc/n32g401_algo_common.h"/>
                <file category="source" name="firmware/n32g401_algo_lib/lib/n32g401_algo_common.lib"/>
            </files>
        </component>
        <component Cclass="Device" Cgroup="Algorithm Libs" Csub="AES" Cversion="1.0.0" condition="N32G401 Algorithm Common">
            <RTE_Components_h>
        #define RTE_DEVICE_ALGO_AES
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_algo_lib/inc/n32g401_aes.h"/>
                <file category="source" name="firmware/n32g401_algo_lib/lib/n32g401_aes.lib"/>
            </files>
        </component>
        <component Cclass="Device" Cgroup="Algorithm Libs" Csub="DES" Cversion="1.0.0" condition="N32G401 Algorithm Common">
            <RTE_Components_h>
        #define RTE_DEVICE_ALGO_DES
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_algo_lib/inc/n32g401_des.h"/>
                <file category="source" name="firmware/n32g401_algo_lib/lib/n32g401_des.lib"/>
            </files>
        </component>
        <component Cclass="Device" Cgroup="Algorithm Libs" Csub="HASH" Cversion="1.0.0" condition="N32G401 Algorithm Common">
            <RTE_Components_h>
        #define RTE_DEVICE_ALGO_HASH
            </RTE_Components_h>
            <files>
                <file category="header" name="firmware/n32g401_algo_lib/inc/n32g401_hash.h"/>
                <file category="source" name="firmware/n32g401_algo_lib/lib/n32g401_hash.lib"/>
            </files>
        </component>
		

    </components>
</package>
