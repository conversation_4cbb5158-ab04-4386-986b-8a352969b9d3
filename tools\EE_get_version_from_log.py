'''
Author: 
Date: 
LastEditors: 
LastEditTime: 
Description: 从CHANGELOG.md中获取版本号, 并调用EE_rw_proj_info.py写入固件源码中
FilePath: ..\tools\\EE_get_version_from_log.py
'''
#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
import sys
import configparser
from datetime import datetime
import subprocess


def read_config(config_file):
    config = configparser.ConfigParser()
    config.read(config_file)

    # required_options = ["proj_ver_file_path", "proj_ver_file_name", "proj_var_name", "sv_var_name", "hv_var_name", "date_var_name", "customerID_var_name"]
    log_required_options = ["log_file_path"]
    # missing_options = [opt for opt in required_options if not config.has_option("PROJECT_VER", opt)]
    log_missing_options = [opt for opt in log_required_options if not config.has_option("LOG", opt)]
    # if missing_options:
    #     raise ValueError(f"Missing required options in config file: {', '.join(missing_options)}")
    if log_missing_options:
        raise ValueError(f"Missing log required options in config file: {', '.join(log_missing_options)}")

    return {
        # "proj_ver_file_path": config.get("PROJECT_VER", "proj_ver_file_path"),
        # "proj_ver_file_name": config.get("PROJECT_VER", "proj_ver_file_name"),
        # "proj_var_name": config.get("PROJECT_VER", "proj_var_name"),
        # "sv_var_name": config.get("PROJECT_VER", "sv_var_name"),        
        # "hv_var_name": config.get("PROJECT_VER", "hv_var_name"),
        # "date_var_name": config.get("PROJECT_VER", "date_var_name"),
        # "customerID_var_name": config.get("PROJECT_VER", "customerID_var_name"),
        "log_file_path": config.get("LOG", "log_file_path"),
    }


def get_latest_version(changelog_path):
    if not os.path.exists(changelog_path):
        return {"version": "0.0.0", "date": "0-0-0"}
    with open(changelog_path, 'r', encoding='utf-8') as f:
        for line in f:
            match = re.match(r'^## \[v([\w\.]+)\] - (\d{4}-\d{2}-\d{2})', line)
            # print(f"line: {line.strip()}, version match: {match}")
                          # 解析和格式化日期
            if match:
                date_str = match.group(2)
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                formatted_date = date_obj.strftime('%y-%m-%d')
                return {
                    "version": match.group(1),
                    "date": formatted_date
                }
    return {"version": "0.0.0", "date": "0-0-0"}

if __name__ == "__main__":
    args = sys.argv
    if len(args) != 3:
        print("EE_get_version_from_log.py: CHANGELOG.md directory path missing.")
        sys.exit(1)

    # 读取项目版本默认参数
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(script_dir)    
    config_path = os.path.join(script_dir, 'config.ini')

    
    config = read_config(config_path)

    # set file path
    target_name = sys.argv[1]
    log_file_path = os.path.abspath(os.path.join(project_dir, config["log_file_path"]))
    changelog_path = os.path.join(log_file_path, target_name, 'CHANGELOG.md')
    print(f"changelog_path: {changelog_path}")

    ## get version
    latest_version_info = get_latest_version(changelog_path)    
    print(f"EE_get_version_from_log.py: {latest_version_info}")

    ## update date to today
    current_date = datetime.now()
    formatted_date = current_date.strftime('%y-%m-%d')
    latest_version_info["date"] = formatted_date

    # update version to local file
    RW_PROJ_INFO_SCRIPT = os.path.join(script_dir, "EE_rw_proj_info.py")
    subprocess.run([sys.executable, RW_PROJ_INFO_SCRIPT, target_name, latest_version_info["version"], latest_version_info["date"]], check=True)
