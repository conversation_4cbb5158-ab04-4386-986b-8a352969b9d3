/*
 * @Author: xx
 * @Date: 2024-05-21 20:06:29
 * @LastEditors: Do not edit
 * @LastEditTime: 2024-07-31 17:15:32
 * @Description:
 * @FilePath: \MDK-ARMd:\Project_data\05_project_code\local_SVN\vscode_workspace\fw01_dToF_lidar\Bsp\src\bsp_freq.c
 */
#include "bsp_freq.h"
#include "timer.h"

// static void (*trigModeCbFunc)(void);

void TrigModeTimer(uint16_t count) {
    MX_TIM3_Init(count);
}

/*static void TrigVoltageTimer(uint16_t ch0, uint16_t ch1)
{
        MX_TIM0_Init(ch0,ch1);
}*/


/**
 * @brief This function handles TIM1 update interrupt and TIM16 global interrupt.
 */
void TIM1_UP_TIM16_IRQHandler(void) {
    /* USER CODE BEGIN TIM1_UP_TIM16_IRQn 0 */

    /* USER CODE END TIM1_UP_TIM16_IRQn 0 */

    /* USER CODE BEGIN TIM1_UP_TIM16_IRQn 1 */
    //	if(LL_TIM_IsActiveFlag_UPDATE(TIM1) == 1) {
    //		LL_TIM_ClearFlag_UPDATE(TIM1);
    //		trigModeCbFunc();
    //	}
    /* USER CODE END TIM1_UP_TIM16_IRQn 1 */
}

/*
void TIM_TRIG_MODE_IRQHandler(void)
{
    if(flag == true) {
        clear(flag);
        trigModeCbFunc();
    }
}
*/
void motor_init(void) {
    motor_pwm_init();
}


// _Bool RegisterTrigModeCallBackFunc(PtrTrigTimer *ptr, void (*trigCallback)(void)) {
//     ptr->IntPtr = TrigModeTimer;
//     // ptr->TrigVoltagePtr = TrigVoltageTimer;
//     // trigModeCbFunc = trigCallback;

//     return true;
// }
