#include "timer.h"
#include "GPIO.h"


uint16_t g_PwmPeriod = 7500;

void TIM6_Configuration(void) {
    TIM_TimeBaseInitType TIM_TimeBaseStructure;


    RCC_APB1_Peripheral_Clock_Enable(RCC_APB1_PERIPH_TIM6);

    TIM_Base_Struct_Initialize(&TIM_TimeBaseStructure);
    TIM_TimeBaseStructure.Period    = 999;
    TIM_TimeBaseStructure.Prescaler = 71;
    TIM_TimeBaseStructure.ClkDiv    = 0;
    TIM_TimeBaseStructure.CntMode   = TIM_CNT_MODE_UP;

    TIM_Base_Initialize(TIM6, &TIM_TimeBaseStructure);

    TIM_Base_Reload_Mode_Set(TIM6, TIM_PSC_RELOAD_MODE_IMMEDIATE);

    NVIC_Configuration(TIM6_IRQn, ENABLE, 2, 2);
    TIM_Interrupt_Enable(TIM6, TIM_INT_UPDATE);

    TIM_On(TIM6);
}
