#ifndef _SPI_H_
#define _SPI_H_

#include "n32g401.h"




void SPI1_Init(void);
uint8_t SPI1_ReadWriteByte(uint8_t TxData,uint8_t* RxData);
uint8_t SPI1_ReadWriteNByte(uint8_t *tx_buff, uint8_t *rx_buff, uint8_t tx_len);
uint8_t SPI1_WriteNByte(uint8_t *tx_buff, uint16_t tx_len);
uint8_t SPI1_ReadNByte(uint8_t *rx_buff, uint16_t rx_len);
void VI4302_CsLow(void);
void VI4302_CsHigh(void);

void SPI_Test(void);





#endif




