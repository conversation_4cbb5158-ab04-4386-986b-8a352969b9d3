/**
 * @file protocol.h
 * <AUTHOR> name (<EMAIL>)
 * @brief 通用协议，与端口分离
 * @version 0.1
 * @date 2025-07-31
 *
 * @copyright Copyright (c) 2025
 *
 */
#ifndef _PROTOCOL_H_
#define _PROTOCOL_H_

#include "typedef.h"


#define USING_CSPC_PROTOCOL 1


// 协议错误码定义
typedef enum {
    PROTOCOL_SUCCESS = 0,
    PROTOCOL_HEADER_ERROR,
    PROTOCOL_CHECKSUM_ERROR,
    PROTOCOL_LENGTH_ERROR,
    PROTOCOL_HANDLER_NOT_FOUND,
} ProtocolError;


typedef enum {
    kOk       = 0x01,
    kResponse = 0x02,
    kWord     = 0x03,
    kHalfWord = 0x04,
    kFdbSize,
} EProtocolAckType;

// 协议消息结构体
typedef struct {
    uint8_t          cmd;
    uint8_t          id;
    const uint8_t *  data;
    uint16_t         length;
    EProtocolAckType ack_type;
} ProtocolMessage;

// 协议处理回调函数类型
typedef ProtocolError (*ProtocolHandlerPtr)(const ProtocolMessage *msg);


/*协议cmd枚举 */
// enum EInteractionFrame {
//     eHEADER       = 0xA5,
//     eACK_HEADER   = 0xA5,
//     eFIXED_LENGTH = 0x06,
//     eXOR_INDEX    = 0x03,  // start at index 0
// };

/*协议cmd枚举*/
enum ECmd {
    eH2D = 0 << 0,  // host->device
    eD2H = 1 << 0,  // device->host

    //    eCMD        = 0<<2 | 0<<1, //cmd
    //    eACK        = 0<<2 | 1<<1, //ack
    //    eINFO       = 1<<2 | 0<<1, //info
    //    eDATA       = 1<<2 | 1<<1, //data

    eW = 0 << 1,  // write
    eR = 1 << 1,  // read

    //    eW          = 0<<3, //write
    //    eR          = 1<<3, //read
};

/*协议cmd枚举*/
enum {
    kH2D = 0x01, /*host->device*/
    kD2H = 0X02, /*device->host*/
    kHWS = 0x04, /*host write success*/
    kHRS = 0x08, /*host read success*/
    kHSS = 0x10, /*host send ok*/
    kDFF = 0x20, /*device fixed frequency send*/
    kDNP = 0x40, /*device not parse logic*/
};

typedef struct {
    uint16_t header;
    uint16_t ack_header;
    uint16_t fixed_length;
    uint8_t  check_index;
    uint8_t  num_multiple;
    //校验类型
} StProtocolInfo;

#if USING_CSPC_PROTOCOL == 1
#define LIDAR_DATA_NUM          25
#define LIDAR_DATABUFF_SIZE_MAX (10 + LIDAR_DATA_NUM * 4)  //
#define INFORMATION             0x83
#else
#define LIDAR_DATA_NUM          12
#define LIDAR_DATABUFF_SIZE_MAX (12 + LIDAR_DATA_NUM * 4)  //
#define LIDAR_DATA_SCAN_INFO    0x07

#endif

#if USING_CSPC_PROTOCOL == 1
#pragma pack(1)
typedef struct {
    uint16_t PH;
    uint8_t  F_And_C;
    uint8_t  LSN;
    uint16_t FSA;
    uint16_t LSA;
    uint16_t CS;
    uint32_t SI[LIDAR_DATA_NUM];
    uint8_t  BufferLen;
    uint8_t  Pack_Send_En;
} PtrRegLidarData;

enum ELidarProt {
    eLIDAR_BEGIN_HEADER = 0x5AA5,
    eLIDAR_TYPE_DATA    = 0x81,
    eLIDAR_TYPE_INFO    = 0x01,
    eLIDAR_FIX_LEN      = 7,
};

#define LIDAR_BEGIN_BUFF_SIZE 20
typedef struct {
    uint16_t header;
    uint16_t num;
    uint16_t checksum;  //除该字段外其他所有字节和
    uint8_t  type;      // 包类型：0x81-雷达数据； 0x01-雷达信息
    uint8_t  data[LIDAR_BEGIN_BUFF_SIZE];
} StBeginProtocol;

#else
typedef struct {
    uint16_t header;
    uint8_t  information;
    uint8_t  data_number;
    uint16_t speed;
    uint16_t first_angle;
    uint32_t data[LIDAR_DATA_NUM];
    uint16_t last_angle;
    uint16_t check_sum;
    uint16_t send_end;
} PtrRegLidarData;
#pragma pack()

#endif


#pragma pack(2)

// interaction protocol
#define INTERACTION_BUFF_SIZE 20
typedef struct _interaction_protocol {
    uint8_t  header;
    uint8_t  cmd;
    uint8_t  id;
    uint8_t  checkXor;
    uint16_t num;  // half word num
    uint8_t  data[INTERACTION_BUFF_SIZE];
} StInteractionProtocol;

// greymap protocol
#define GreyMap_BUFF_SIZE 25  // 5* 5
typedef struct _greymap_protocol {
    uint8_t  header;
    uint8_t  cmd;
    uint8_t  id;
    uint8_t  checkXor;
    uint16_t num;
    uint16_t data[GreyMap_BUFF_SIZE];
} StGreyMapProtocol;

// histogram protocol
#define HISTOGRAM_BUFF_SIZE 2048
typedef struct _histogram_protocol {
    uint8_t  header;
    uint8_t  cmd;
    uint8_t  id;
    uint8_t  checkXor;
    uint16_t num;
    uint8_t  data[HISTOGRAM_BUFF_SIZE];
} StHistogramProtocol;

// range protocol
#define RANGE_BUFF_SIZE 23
typedef struct _range_protocol {
    uint8_t  header;
    uint8_t  cmd;
    uint8_t  id;
    uint8_t  checkXor;
    uint16_t num;
    uint8_t  data[RANGE_BUFF_SIZE];
} StRangeProtocol;

// scan points protocol
#define SCAN_POINTS_BUFF_SIZE 20
typedef struct _scan_points_protocol {
    uint8_t  header;
    uint8_t  cmd;
    uint8_t  id;
    uint8_t  checkXor;
    uint16_t num;
    uint8_t  data[SCAN_POINTS_BUFF_SIZE];
} StScanPointsProtocol;

/**
 * @brief 协议类型，为了向下兼容，不同模式采用了不同的协议结构体，实际协议可能相同
 *
 */
typedef enum {
    ePROT_INTERACTION = 0,
    ePROT_GREYMAP,
    ePROT_HISTOGRAM,
    ePROT_RANGE,
    ePROT_SCANPOINTS,
    ePROT_LIDAR_BEGIN,
    ePROT_DEVICE_INFO,
} EProtocolType;

/**
 * @brief 指令标识
 *
 */
typedef enum {
    eFRAME_IDLE = 0,
    eFRAME_REC,
    eFRAME_SEND,
} EFrameFlag;


/**
 * @brief 缓存指令信息
 *
 */
typedef struct {
    EProtocolType type;
    uint16_t      length;
    EFrameFlag    frame_flag;
} StFrameInfo;


#define MAX(a, b)     ((a) > (b) ? (a) : (b))
#define Com_BUFF_SIZE MAX(MAX(MAX(GreyMap_BUFF_SIZE << 1, HISTOGRAM_BUFF_SIZE), RANGE_BUFF_SIZE), SCAN_POINTS_BUFF_SIZE)

#if 1
/**
 * @brief 发送数据buffer
 *
 */
typedef union {
    uint8_t               buffer[Com_BUFF_SIZE];
    StInteractionProtocol interaction;
    StGreyMapProtocol     greymap;
    StHistogramProtocol   histogram;
    StRangeProtocol       range;
    StScanPointsProtocol  scan_points;
    PtrRegLidarData       lidar_data;
    StBeginProtocol       lidar_begin;
} UCommBuffer;

#else
/**
 * @brief
 *
 */
typedef struct {
    union {
        uint8_t               buffer[Com_BUFF_SIZE];
        StInteractionProtocol interaction;
        StGreyMapProtocol     greymap;
        StHistogramProtocol   histogram;
        StRangeProtocol       range;
        StScanPointsProtocol  scan_points;
    };
    uint16_t rec_buffe_size[20];
    uint8_t  rec_count;
} StComFifo;
#endif
#pragma pack()

/**
 * @brief 业务处理回调函数
 *
 */
// typedef struct {
// } Protocol_Handler_T;

/**
 * @brief 通讯协议
 *
 */
typedef struct {
    void (*init)(void);  //协议初始化
    StProtocolInfo (*get_protocol_info)(EProtocolType);
    bool (*is_valid_frame)(uint16_t);
    uint16_t (*get_frame_data_area_num)(const StInteractionProtocol *const);
    ProtocolError (*encode)(ProtocolMessage *, StInteractionProtocol *);
    ProtocolError (*decode_one_frame)(const StInteractionProtocol *const);
    ProtocolError (*decode_multi_frame)(const uint8_t *const, uint16_t);
    ProtocolError (*register_handler)(ProtocolHandlerPtr);  // 注册协议处理器
} API_Protocol_T;


extern const API_Protocol_T *const g_protocol_ptr;

#endif
