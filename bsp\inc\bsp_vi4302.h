#ifndef __BSP_VI4302_H__
#define __BSP_VI4302_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "variable_table.h"

/*VI4302 frame output mode*/
enum{
		kSingleFrame = 1,
		kMultFrame	 = 2,
		kFrameSize
};

/*VI4302 MP type select*/
enum{
		kNormalMp = 1,
		kAttenuation = 2,
		kReference = 3,
		kMpSize
};

/*Vi4302 mode type*/
enum{
		kSinglePixel = 1,
		kRanging = 2,
		kModeType
};

typedef enum {
		kHistNoraml = 0x11,
		kHistAtten = 0x12,
		kHistRef = 0x13,
}hist_channel;


// typedef struct {
// 	uint8_t parameter_types;                                                       
//     uint8_t s_mode;                                                       
//     uint8_t d_mode;                                                      
//     uint8_t mp_num;                                                              
//     uint16_t xtalk_bin;                                                       
//     uint16_t xtalk_peak;   
//     uint16_t xtalk_offset;// 
// 	uint16_t xtalk_calc_cnt;	

// }xtalk_param_t;


typedef struct {
    uint16_t parameter_types;
	uint8_t  para_S;
	uint8_t  para_D;
	uint8_t  para_MP;
	uint16_t xtalk_bin;
	uint16_t xtalk_peak;
	uint8_t  xtalk_rang_cnt;
	uint8_t  confidence_mode;
	uint16_t xtalk_calc_cnt;


	uint8_t  flag_1_gap;
	uint8_t  flag_1_peak_ratio; 
	uint8_t	 flag_2_gap; 
	uint8_t	 confidence_k;
	
} xtalk_param_t;

/*************/
#define SPI_4302_REG_READ 						0x00
#define SPI_4302_REG_WRITE						0x01
#define SPI_4302_HIST_READ 						0x03
#define SPI_4302_LOAD_FIRMWARE 				0x04
#define SPI_4302_READ_FIRMWARE				0x05
#define SPI_4302_UNLOCK_FIRMWARE 			0x06
#define SPI_4302_LOCK_FIRMWARE 				0x07
#define SPI_4302_WRITE_DRAM 					0x08
#define SPI_4302_READ_DRAM 						0x09
#define SPI_4302_FIRMWARE_SUCCESS			0xAA

/************/
#define Vi4302_REGISTER_LOW_ADDR			0xFD
#define Vi4302_REGISTER_HIGH_ADDR			0x12A
#define VI4302_GREYMAP_REGISTER_CNT		24+2//+1
#define VI4302_RANGING_REGISTER_CNT		12+2+36+1+5//+8



/************/
#define XTALK_CONFIG_CMD  	0X43
#define XTALK_CAL_CMD     	0X44
#define S_PARAMETER       	0X01
#define D_PARAMETER       	0X02
#define MP_PARAMETER      	0X04
#define XTALK_BIN         	0X08
#define XTALK_PEAK        	0X10
#define GAP_1      	        0X20
#define RAITO      	        0X40
#define XTALK_RANG_CNT      0X80
#define CONFIDENCE_K        0X100         
#define GAP_2               0X200 
#define CONFIDENCE_MODE     0X400 


/************/
#define SPI_CS_LOW										GPIO_Pins_Reset(GPIOA,GPIO_PIN_15);
#define SPI_CS_HIGH										GPIO_Pins_Set(GPIOA,GPIO_PIN_15);
	



uint8_t Vi4302FaculaRegisterInit(uint8_t type);
int8_t Vi4302SelectFrame(uint8_t frame, uint8_t enable);
int8_t Vi4302SetFrameFormat(uint8_t mpType, uint8_t tofChannel, uint8_t peakChannel, uint8_t multshotNoiseChannel);
int8_t Vi4302MpOpen(uint8_t index);
uint8_t Vi4300GetFrameCnt(void);
int8_t Vi4300GetMpPeak(uint8_t *buff, uint16_t startAddr, uint8_t size);
int Vi4302McuFirmwareLoad(void);
int Vi4302McuFirmwareHistLoad(void);
int8_t VI4302_Temp_Bvd(void);
uint8_t VbdAutoCalculateByCmd(void);
int8_t VbdAutoCalculate(uint8_t *step);
int8_t tdcAutoCalculate(uint8_t *tdc);
int8_t FrameSetting(uint16_t fps, uint16_t *ackFps);
int8_t Vi4302StartRanging(void);
int8_t Vi4302StopRanging(void);
int8_t Vi4302StartHist(hist_channel channel);
int8_t Vi4302FpsConfig(uint16_t delay);
int8_t vbdMultiCalc(void);
int8_t xtalk_init(xtalk_param_t *param);
int8_t xtalk_calc(uint8_t bin_num, uint8_t *x_bin,uint16_t *x_peak);
int8_t search_logic_init(uint8_t conf_flg);
int8_t range_cnt_init(uint8_t cnt);
void Vi4302ExeVbdTdc(bool is_swith);
void Vi4302RangingConfig(void);


#ifdef __cplusplus
}
#endif

#endif 
