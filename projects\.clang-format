---
Language: Cpp
# 圆括号之后，多行内容，对齐
AlignAfterOpenBracket: Align

# 连续赋值时，对齐所有等号
AlignConsecutiveAssignments: true
# 连续声明时，对齐所有声明的变量名
AlignConsecutiveDeclarations: true

# 连续宏定义时，对齐所有定义值
AlignConsecutiveMacros: AcrossEmptyLinesAndComments
# 对齐操作数
AlignOperands: Align
# 对齐连续的尾随注释
AlignTrailingComments: true
# 允许将一个函数声明的所有参数移到下一行
AllowAllParametersOfDeclarationOnNextLine: false
# 将简单的语句块放到一个单行
AllowShortBlocksOnASingleLine: false
# 短 if 语句放在单行
AllowShortIfStatementsOnASingleLine: Never
# 如果为 false，函数调用的参数要么全部在同一行，要么各有一行
BinPackArguments: false
# 如果为 false，函数声明或函数定义的参数要么全部在同一行，要么各有一行
BinPackParameters: false

# 控制单独的大括号换行事件，只有当 Attach: 函数尾部
BreakBeforeBraces: Attach
# 函数内容会换一行开始
AllowShortFunctionsOnASingleLine: None

BraceWrapping:
    AfterControlStatement: true # 控制语句 (if/for/while/switch/..) 换行
    AfterEnum: true # 枚举定义换行
    AfterFunction: true # 函数定义换行
    AfterStruct: true # 结构定义换行
    AfterUnion: true # 共同体定义换行
    BeforeElse: true # 在 else 之前换行
    IndentBraces: false # 换行大括号缩进
    SplitEmptyFunction: true # 空函数是否可以放在单行
    SplitEmptyRecord: true # 空类，结构或联合主体是否可以放在单行
    SplitEmptyNamespace: true # 空 namespace 是否可以放在单行

# 括号前空格
SpaceBeforeParens: ControlStatements
# 指针对齐：右
PointerAlignment: Right
# 三元运算符将被放置在换行后
BreakBeforeTernaryOperators: true
# Tab缩进：4
TabWidth: 4
# 每行字符的限制，0 表示没有限制
ColumnLimit: 160
# 缩进空格宽度：4
IndentWidth: 4
# 保留在赋值操作符之前的空格
SpaceBeforeAssignmentOperators: true
# 排序 include 的头文件
SortIncludes: true
# 允许重新排版注释
ReflowComments: true
# 尾行注释前的空格数
SpacesBeforeTrailingComments: 2
# 连续空行的最大数量
MaxEmptyLinesToKeep: 2
# 使用 tab 字符: Never 从不使用, ForIndentation 仅在缩进时使用制表符，ForContinuationAndIndentation, Always
UseTab: Never
# SpacesInParentheses 如果为 true, 将会在 “(” 之后和 “)” 之前插入空格
SpacesInParentheses: false
# SpacesInSquareBrackets 如果为 true, 将会在 “[" 之后和 “]” 之前插入空格
SpacesInSquareBrackets: false
