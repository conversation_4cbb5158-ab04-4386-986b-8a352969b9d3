#include "facula_process.h"
#include "bsp_adc.h"
#include "bsp_io.h"
#include "bsp_vi4302.h"
#include "scan_process.h"

static uint8_t mpBuff[20] = {0};

#define SPAD_COUNT_TIME_10MS      (0X00171e8f)
#define GREYMAP_START_STORGE_ADDR 500


static int8_t ConfigMp(void) {
    Vi4302SelectFrame(kMultFrame, false);                   // stream off
    Vi4302SetFrameFormat(kNormalMp, 0, SET_8BIT(0), 0);     // first peak channel
    Vi4302SetFrameFormat(kAttenuation, 0, SET_8BIT(0), 0);  // first peak
                                                            // channel
    Vi4302SetFrameFormat(kReference, 0, 0, 0);
    return 1;
}

static uint8_t  frameCnt  = 0;
static uint8_t  sampleCnt = 0;
static uint32_t peak_sum  = 0;
static int8_t   GetMpPeak(uint32_t *peak) {
    int8_t   status  = -1;
    uint8_t  n       = 0;  //,value=0;
    uint32_t timeOut = 0;

    g_ptrSysFlag.isInterruptTrig = false;
    peak_sum                     = 0;
    sampleCnt                    = 0;
    frameCnt                     = 0;

    // printf("************\n");
    for (uint8_t mpIndex = 0; mpIndex < MP_MAX; mpIndex++) {
        Vi4302SelectFrame(kMultFrame, false);
        status = Vi4302MpOpen(mpIndex);
        Vi4302SelectFrame(kMultFrame, true);
        timeOut = 0;

        while (1) {
            if (g_ptrSysFlag.isInterruptTrig == true) {
                g_ptrSysFlag.isInterruptTrig = false;
                timeOut                      = 0;
                frameCnt                     = Vi4300GetFrameCnt();
                if (frameCnt == 0) {
                    continue;
                }

                for (n = 0; n < frameCnt; n++) {
                    memset(mpBuff, 0, 7);
                    g_ptrSpi.wRegister(0xd7, 0x01, NULL);
                    Vi4300GetMpPeak(mpBuff, Vi4302_REGISTER_HIGH_ADDR - 3, 4);

                    if (sampleCnt < 10) {
                        if (mpIndex == 0 || mpIndex == 4 || mpIndex == 20 || mpIndex == 24) {
                            peak_sum += (uint16_t)(mpBuff[5] | mpBuff[6] << 8);
                        } else {
                            peak_sum += (uint16_t)(mpBuff[3] | mpBuff[4] << 8);
                        }
                        // printf("mpIndex %d value %d\n",mpIndex,peak_sum);
                        sampleCnt++;
                    } else {
                        break;
                    }
                }
                if (sampleCnt == 10) {
                    peak[mpIndex] = peak_sum / 10;
                    // printf("mpIndex %d value %d\n",mpIndex,peak_sum);
                    // printf("*******\n");
                    peak_sum  = 0;
                    sampleCnt = 0;
                    break;
                }
            }
            timeOut++;
            if (timeOut >= 100000) {
                return -1;
            }
        }
    }
    return status;
}

void FaculaProcess(void) {
    // uint8_t vbdStep = 0;
    int8_t vbdStatus = -1;
    // uint8_t timeOutCnt = 0;
    //_Bool  timeOutBreak = false;
    uint8_t m = 0;
    // uint8_t vbdTemp=0;
    uint8_t rVall[2] = {0};

    // enable vld
    VLD_ENABLE;

    // reset vi4302
    RESET_4302_LOW

    PROCESS_DELAY(5);

    // enable vi4302
    RESET_4302_HIGHT

    PROCESS_DELAY(10);

    // spi register test
    /*vbdStatus = g_ptrSpi.wRegister(0x024F,0x80,NULL);
    if(vbdStatus == -1) {
            g_ptrSysFlag.errorCode |= SET_16BIT(0);
    }*/

    // init register
    vbdStatus = 0;

    for (m = 0; m < 3; m++) {
        vbdStatus = Vi4302FaculaRegisterInit(kSinglePixel);
        if (vbdStatus == 1) {
            break;
        }
    }
    if (vbdStatus != 1) {
        g_ptrSysFlag.errorCode |= SET_16BIT(3);
    }

    if ((g_ptrSysParam.vbdStep == 0x00 && g_ptrSysParam.vbdTmp == 0x00 && g_ptrSysParam.tdcTmp == 0x00) ||
        (g_ptrSysParam.vbdStep == 0xFF && g_ptrSysParam.vbdTmp == 0xFF && g_ptrSysParam.tdcTmp == 0xFF)) {
        g_ptrSysFlag.errorCode |= SET_16BIT(1);
    } else {
        // setting vbd voltage
        g_ptrSpi.wRegister(0x0242, g_ptrSysParam.tdcTmp, NULL);
        g_ptrSpi.wRegister(0x024F, g_ptrSysParam.vbdStep, NULL);
    }

    // config MP
    vbdStatus = ConfigMp();
    if (vbdStatus == -1) {
        g_ptrSysFlag.errorCode |= SET_16BIT(3);
    }
    // what happen when ts is bad?
    g_ptrSysParam.currentTemperature = GettingAdcValue();
    g_ptrSysParam.temperatureLast    = g_ptrSysParam.currentTemperature;
    g_ptrDDSInfoCount.tempCnt        = getAbsolutionTime();

    for (;;) {
        g_ptrTimeTask.TaskPoll();

        ParseAck();

        if (g_ptrSysFlag.isDetectError == false) {
            if (GetMpPeak((uint32_t *)&cache[GREYMAP_START_STORGE_ADDR]) == 1) {
                // transmit peak data
                if (g_regVi4302.isWriteReg == true) {
                    g_regVi4302.isWriteReg = false;
                    g_ptrSpi.wRegister(g_regVi4302.buff[1] | g_regVi4302.buff[2] << 8, g_regVi4302.buff[0], NULL);
                }

                if (g_regVi4302.isReadReg == true) {
                    g_regVi4302.isReadReg = false;
                    g_ptrSpi.rRegister(g_regVi4302.buff[1] | g_regVi4302.buff[2] << 8, &rVall[0], NULL);
                    g_protocol_ptr->encode(kD2H | kHRS | kHSS, 0xAB, (uint8_t *)&rVall, REGISTER_BYTE_CNT, kResponse);
                }

                g_protocol_ptr->encode(kD2H | kDFF, 0xAD, &cache[GREYMAP_START_STORGE_ADDR], 100, kWord);  // 4*25
            } else {
                // report error message
                g_ptrSysFlag.errorCode |= SET_16BIT(3);
            }
        } else {
            DDSCodePackage(LidarSendBuf);
            g_ptrCom.ComTransmitDmaData(LidarSendBuf, 11);
            PROCESS_DELAY(100);
        }
    }
}
