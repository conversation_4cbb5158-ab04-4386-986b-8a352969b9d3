
/**
*\*\file      main.h
*\*\author    Nations
*\*\version   v1.0.0
*\*\copyright Copyright (c) 2022, Nations Technologies Inc. All rights reserved. 
*/
#ifndef __MAIN_H__
#define __MAIN_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "n32g401_spi.h"
#include "stdio.h"
#include "math.h"
#include "string.h"
#include "GPIO.h"
#include "usart.h"
#include "spi.h"
#include "timer.h"
#include "flash.h"
#include "delay.h"
#include "WDT.h"


typedef enum
{
    FAILED = 0,
    PASSED = !FAILED
} Status, TestStatus;

//#define HARDWARE_NSS

#define TRUE	1
#define FALSE	0




#ifdef __cplusplus
}
#endif




#endif /* __MAIN_H__ */


