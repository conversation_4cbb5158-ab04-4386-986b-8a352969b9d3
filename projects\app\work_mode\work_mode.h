#ifndef _WORK_MODE_H_
#define _WORK_MODE_H_

#include <stdlib.h>
#include "n32g401.h"
#include "A2_Configurable.h"


#pragma pack(4)
									  
typedef struct
{
	uint8_t work_mode;              // sensor work mode, could be _SINGLE_PIXEL_MODE or _RANG_MODE
	uint8_t sensor_is_calibrated;   // whether sensor is calibrated
	uint8_t sensor_cal_tdc;         // sensor calibrated tdc value 
	uint8_t sensor_cal_bvd;         // sensor calibrated bvd value
	int8_t  sensor_cal_ts;          // temperature when sensor is calibrating bvd
	uint8_t xtalk_is_calibrated;
	uint8_t xtalk_is_write;
	
	A2_Configurable_Parameters  sys_A2_Conf_Para;		
} system_config_t;

#pragma pack()


extern system_config_t g_sys_cfg;



#endif