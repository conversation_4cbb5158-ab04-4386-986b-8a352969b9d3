#ifndef _RING_BUFF_H
#define _RING_BUFF_H

#include <stdbool.h>
#include <stdint.h>

typedef enum {
    RING_BUFF_OK        = 1,   // 操作成功
    RING_BUFF_ERR_NULL  = -1,  // 空指针错误
    RING_BUFF_ERR_FULL  = -2,  // 缓冲区满
    RING_BUFF_ERR_EMPTY = -3,  // 缓冲区空
    RING_BUFF_ERR_PARAM = -4   // 参数错误
} RingBuff_Status_t;


#ifndef RING_BUFFER_BUFF_SIZE
#define RING_BUFFER_BUFF_SIZE 1152  // 0x480
#endif

typedef struct {
    uint16_t in;                             //
    uint16_t out;                            //
    uint8_t  buffer[RING_BUFFER_BUFF_SIZE];  //
} StRingBuff;


typedef struct {
    RingBuff_Status_t (*ringbuff_read_data)(StRingBuff *, uint8_t *, uint16_t);
    RingBuff_Status_t (*ringbuff_read_specific_data)(StRingBuff *, uint8_t *, uint16_t, uint8_t,  uint8_t *const);
    void (*ringbuff_write_data)(StRingBuff *, uint8_t *, uint16_t);
} API_Ringbuff_T;

extern const API_Ringbuff_T *const g_ringBuffer_ptr;

#endif
