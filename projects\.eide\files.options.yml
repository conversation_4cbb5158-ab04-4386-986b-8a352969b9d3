##########################################################################################
#                        Append Compiler Options For Source Files
##########################################################################################

# syntax:
#   <your pattern>: <compiler options>
# For get pattern syntax, please refer to: https://www.npmjs.com/package/micromatch
#
# examples:
#   'main.cpp':           --cpp11 -Og ...
#   'src/*.c':            -gnu -O2 ...
#   'src/lib/**/*.cpp':   --cpp11 -Os ...
#   '!Application/*.c':   -O0
#   '**/*.c':             -O2 -gnu ...

version: "2.1"
options:
    N32G401:
        files: {}
        virtualPathFiles: {}
    APP:
        files: {}
        virtualPathFiles: {}
    APP_NO_WDT:
        files: {}
        virtualPathFiles: {}
    A5_RLT:
        files: {}
        virtualPathFiles: {}
