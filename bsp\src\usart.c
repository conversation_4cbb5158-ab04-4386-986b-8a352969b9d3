#include "usart.h"

//************************** */
#include "GPIO.h"
#include "delay.h"
#include "flash.h"
#include "typedef.h"
#include <stdio.h>
#include <string.h>

//*************************** */
#include "cmd_handle.h"
#include "uartfifo.h"


static UsartRecIntrCallbackPtr s_rec_intr_callback_ptr = NULL;

uint8_t          UART2_RecieveBuff[UARTINFIFO_SIZE]  = {0x00};
uint8_t          UART2_SendBuff[UARTOUTFIFO_LEN - 1] = {0x00};
volatile uint8_t UART2_SendFlag                      = 0;

void USART2_Init(uint32_t bound_rata) {
    GPIO_InitType  GPIO_InitStructure;
    USART_InitType USART_InitStructure;

    uart1infifo_Clear();
    uart1outfifo_Clear();

    /* DMA clock enable */
    RCC_AHB_Peripheral_Clock_Enable(RCC_AHB_PERIPH_DMA);
    /* Enable GPIO clock */
    RCC_AHB_Peripheral_Clock_Enable(RCC_AHB_PERIPH_GPIOB);
    RCC_APB2_Peripheral_Clock_Enable(RCC_APB2_PERIPH_AFIO);
    /* Enable USARTy and USARTz Clock */
    RCC_APB1_Peripheral_Clock_Enable(RCC_APB1_PERIPH_USART2);

    /* Initialize GPIO_InitStructure */
    GPIO_Structure_Initialize(&GPIO_InitStructure);

    /* Configure USARTy Tx as alternate function push-pull */
    GPIO_InitStructure.Pin            = GPIO_PIN_4;
    GPIO_InitStructure.GPIO_Mode      = GPIO_MODE_AF_PP;
    GPIO_InitStructure.GPIO_Pull      = GPIO_PULL_UP;
    GPIO_InitStructure.GPIO_Alternate = GPIO_AF5_USART2;
    GPIO_Peripheral_Initialize(GPIOB, &GPIO_InitStructure);

    GPIO_InitStructure.Pin = GPIO_PIN_5;
    //	GPIO_InitStructure.GPIO_Mode      = GPIO_MODE_AF_PP;
    GPIO_InitStructure.GPIO_Alternate = GPIO_AF7_USART2;
    GPIO_Peripheral_Initialize(GPIOB, &GPIO_InitStructure);

    /* USARTy and USARTz configuration */
    USART_InitStructure.BaudRate            = bound_rata;
    USART_InitStructure.WordLength          = USART_WL_8B;
    USART_InitStructure.StopBits            = USART_STPB_1;
    USART_InitStructure.Parity              = USART_PE_NO;
    USART_InitStructure.HardwareFlowControl = USART_HFCTRL_NONE;
    USART_InitStructure.Mode                = USART_MODE_RX | USART_MODE_TX;

    /* Configure USARTy and USARTz */
    USART_Initializes(USART2, &USART_InitStructure);
    USART2_DmaConfig();

    /* Enable USARTy DMA Rx and TX request */
    USART_DMA_Transfer_Enable(USART2, USART_DMAREQ_TX | USART_DMAREQ_RX);

    /* Enable the USARTz Receive Interrupt */
    USART_Interrput_Enable(USART2, USART_INT_IDLEF);  //串口空闲中断
                                                      //	NVIC_Configuration(USART2_IRQn, ENABLE, 0, 2);
    NVIC_Configuration(USART2_IRQn, ENABLE, 4, 2);
    USART_Enable(USART2);
}

void USART2_DmaConfig(void) {
    DMA_InitType DMA_InitStructure;

    /* USARTz TX DMA1 Channel (triggered by USARTz Tx event) Config */
    DMA_Reset(DMA_CH3);
    DMA_InitStructure.PeriphAddr     = (USART2_BASE + 0x04);
    DMA_InitStructure.MemAddr        = (uint32_t)UART2_SendBuff;
    DMA_InitStructure.Direction      = DMA_DIR_PERIPH_DST;
    DMA_InitStructure.BufSize        = UARTOUTFIFO_LEN - 1;
    DMA_InitStructure.PeriphInc      = DMA_PERIPH_INC_MODE_DISABLE;
    DMA_InitStructure.MemoryInc      = DMA_MEM_INC_MODE_ENABLE;
    DMA_InitStructure.PeriphDataSize = DMA_PERIPH_DATA_WIDTH_BYTE;
    DMA_InitStructure.MemDataSize    = DMA_MEM_DATA_WIDTH_BYTE;
    DMA_InitStructure.CircularMode   = DMA_CIRCULAR_MODE_DISABLE;
    DMA_InitStructure.Priority       = DMA_CH_PRIORITY_HIGHEST;
    DMA_InitStructure.Mem2Mem        = DMA_MEM2MEM_DISABLE;
    DMA_Initializes(DMA_CH3, &DMA_InitStructure);
    DMA_Channel_Request_Remap(DMA_CH3, DMA_REMAP_USART2_TX);

    /* USARTz RX DMA1 Channel (triggered by USARTz Rx event) Config */
    DMA_Reset(DMA_CH4);
    DMA_InitStructure.PeriphAddr     = (USART2_BASE + 0x04);
    DMA_InitStructure.MemAddr        = (uint32_t)UART2_RecieveBuff;
    DMA_InitStructure.Direction      = DMA_DIR_PERIPH_SRC;
    DMA_InitStructure.BufSize        = UARTINFIFO_SIZE;
    DMA_InitStructure.PeriphInc      = DMA_PERIPH_INC_MODE_DISABLE;
    DMA_InitStructure.MemoryInc      = DMA_MEM_INC_MODE_ENABLE;
    DMA_InitStructure.PeriphDataSize = DMA_PERIPH_DATA_WIDTH_BYTE;
    DMA_InitStructure.MemDataSize    = DMA_MEM_DATA_WIDTH_BYTE;
    DMA_InitStructure.CircularMode   = DMA_CIRCULAR_MODE_DISABLE;
    DMA_InitStructure.Priority       = DMA_CH_PRIORITY_HIGHEST;
    DMA_InitStructure.Mem2Mem        = DMA_MEM2MEM_DISABLE;
    DMA_Initializes(DMA_CH4, &DMA_InitStructure);

    DMA_Channel_Request_Remap(DMA_CH4, DMA_REMAP_USART2_RX);

    DMA_Channel_Enable(DMA_CH4);
    DMA_Channel_Disable(DMA_CH3);
    DMA_Interrupts_Enable(DMA_CH3, DMA_INT_TXC);  //发送完成中断
                                                  //	NVIC_Configuration(DMA_Channel3_IRQn, ENABLE, 0, 3);
    NVIC_Configuration(DMA_Channel3_IRQn, ENABLE, 1, 3);
}

//串口1发送数据 语音数据发送
void UART2_SendData(uint8_t *buffer, uint16_t size) {
    uint16_t i = 0;

    // 强制等待上一次DMA完成（最多阻塞一帧时间）
    while (UART2_SendFlag)
        ;

    if (size != 0) {
        uart1outfifo_DataIn(buffer, size);
    } else {
        UART2_SendFlag = 0;

        return;
    }

    //	if(UART2_SendFlag == 1)
    //	{
    //		return;
    //	}

    if (uart1outfifo_HaveData() != 0) {
        // --- Step1: 清 DMA 状态 ---
        DMA_Channel_Disable(DMA_CH3);
        DMA_Interrupt_Status_Clear(DMA, DMA_CH3_INT_TXC);  // 清除 DMA 中断标志

        // --- Step2: 从 FIFO 中取数据准备发送 ---
        i = uart1outfifo_DataOut(UART2_SendBuff);
        if (i == 0)
            return;
        UART2_SendFlag = 1;

        // --- Step3: 配置发送长度 ---
        DMA_Buffer_Size_Config(DMA_CH3, i);

        // --- Step4: 重新 Enable DMA 通道 ---
        DMA_Channel_Enable(DMA_CH3);
    } else {
        return;
    }
}

void USART2_IRQHandler(void) {
    uint16_t tmp   = 0;
    uint16_t t_dat = 0;

    if (USART_Interrupt_Status_Get(USART2, USART_INT_IDLEF) != RESET) {
        USART_Interrupt_Status_Clear(USART2, USART_INT_IDLEF);
        USART_Data_Receive(USART2);

        DMA_Channel_Disable(DMA_CH4);

        tmp = UARTINFIFO_SIZE - DMA_Current_Data_Transfer_Number_Get(DMA_CH4);

        //		uart1infifo_DataIn(UART2_RecieveBuff, tmp);
        if (s_rec_intr_callback_ptr != NULL) {
            s_rec_intr_callback_ptr(tmp, UART2_RecieveBuff);  // 存放ring buffer
        }
        DMA_Memory_Address_Config(DMA_CH4, (uint32_t)&UART2_RecieveBuff[0]);
        DMA_Buffer_Size_Config(DMA_CH4, UARTINFIFO_SIZE);
        DMA_Channel_Enable(DMA_CH4);
    }

    if (USART_Interrupt_Status_Get(USART2, USART_INT_CTSF) != RESET) {
        USART_Interrupt_Status_Clear(USART2, USART_INT_CTSF);
        t_dat = USART2->DAT;
    }

    if (USART_Interrupt_Status_Get(USART2, USART_INT_OREF) != RESET) {
        USART_Interrupt_Status_Clear(USART2, USART_FLAG_OREF);
        t_dat = USART2->DAT;
    }

    if (USART_Interrupt_Status_Get(USART2, USART_INT_NEF) != RESET) {
        USART_Interrupt_Status_Clear(USART2, USART_INT_NEF);
        t_dat = USART2->DAT;
    }

    if (USART_Interrupt_Status_Get(USART2, USART_INT_FEF) != RESET) {
        USART_Interrupt_Status_Clear(USART2, USART_INT_FEF);
        t_dat = USART2->DAT;
    }

    if (USART_Interrupt_Status_Get(USART2, USART_INT_PEF) != RESET) {
        USART_Interrupt_Status_Clear(USART2, USART_INT_PEF);
        t_dat = USART2->DAT;
    }
}

/**
 * @brief DMA 发送中断
 *
 */
void DMA_Channel3_IRQHandler(void) {
    uint16_t i = 0;

    if (DMA_Interrupt_Status_Get(DMA, DMA_CH3_INT_TXC) != RESET) {
        // DMA发送完成中断
        DMA_Interrupt_Status_Clear(DMA, DMA_CH3_INT_TXC);
        DMA_Channel_Disable(DMA_CH3);

        if (uart1outfifo_HaveData() != 0) {
            // --- Step1: 清 DMA 状态 ---
            DMA_Channel_Disable(DMA_CH3);
            DMA_Interrupt_Status_Clear(DMA, DMA_CH3_INT_TXC);  // 清除 DMA 中断标志

            // --- Step2: 从 FIFO 中取数据准备发送 ---
            i = uart1outfifo_DataOut(UART2_SendBuff);
            if (i == 0)
                return;
            UART2_SendFlag = 1;

            // --- Step3: 配置发送长度 ---
            DMA_Buffer_Size_Config(DMA_CH3, i);

            // --- Step4: 重新 Enable DMA 通道 ---
            DMA_Channel_Enable(DMA_CH3);
        } else {
            UART2_SendFlag = 0;
        }
    }
}


int fputc(int ch, FILE *f) {
    uint8_t p_d[1] = {0};
    p_d[0]         = ch;
    UART2_SendData(p_d, 1);

    return ch;
}

void USART2_RecHandlePtrRegister(UsartRecIntrCallbackPtr handleRecCallback) {
    s_rec_intr_callback_ptr = handleRecCallback;
}

void WaitTransmit(void) {
    // while(g_ptrSysFlag.isComTransmitFinished == false){
    while (DMA_Flag_Status_Get(DMA, DMA_CH3_TXCF) == RESET) {
        delay_ms(1);
    }
}