#include "WDT.h"


void WDT_Init(void) {
#ifdef WDT_DEF
    /* Enable the LSI OSC */
    RCC_LSI_Enable();

    /* Wait till LSI is ready */
    while (RCC_Flag_Status_Get(RCC_FLAG_LSIRD) == RESET) {
    }

    /* IWDG timeout equal to 250 ms (the timeout may varies due to LSI frequency
        dispersion) */
    /* Enable write access to IWDG_PR and IWDG_RLR registers */
    IWDG_Write_Protection_Disable();
    /* IWDG counter clock: LSI/32 */
    IWDG_Prescaler_Division_Set(IWDG_CONFIG_PRESCALER_DIV32);
    /* Set counter reload value to obtain 250ms IWDG TimeOut.
       Counter Reload Value = 250ms/IWDG counter clock period
                            = 250ms / (LSI/32)
                            = 0.25s / (LsiFreq/32)
                            = LsiFreq/(32 * 4)
                            = LsiFreq/128 */
    IWDG_Counter_Reload(40000 / 16);  // 2s
    /* Reload IWDG counter */
    IWDG_Key_Reload();
    /* Enable IWDG (the LSI oscillator will be enabled by hardware) */
    IWDG_Enable();
#endif
}

void WDT_Week(void) {
#ifdef WDT_DEF
    IWDG_Key_Reload();
#endif
}
