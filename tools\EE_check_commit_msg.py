'''
Author: 
Date: 
LastEditors: 
LastEditTime: 
Description: 检查commit msg是否符合 angular 标准
FilePath: ..\tools\\check_commit_msg.py
'''
#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re
import sys
import os
import subprocess
import configparser
import json

#*******************************
#不同项目变更变量:
# 1. 子程序目录
# 2. 
#*******************************
# SCRIPT_FOLDER_NAME = "script_file" # 脚本文件目录
# GENERATE_LOG_SCRIPT_FILE_NAME = os.path.join(SCRIPT_FOLDER_NAME, "EE_generate_changeLog.py")
#****************************

COMMIT_MSG_FILE = sys.argv[1]

# 运行程序合集 target_lists: main-通用变更, 其他项目变更;
TARGET_LISTS = ['main']
# 提交消息的规范正则表达式
COMMIT_MSG_REGEX_TEMPLATE = r'^\[({targets})\](feat|fix|refactor|docs|style|test|chore)(\([\w\-]+\))?: .{{1,50}}'

def update_targets(eide_file_path):
    global TARGET_LISTS, COMMIT_MSG_REGEX

    with open(eide_file_path, 'r', encoding='utf-8') as file:
        eide_data = json.load(file)
    
    target_names = list(eide_data.get("targets", {}).keys())
    TARGET_LISTS.extend(target_names)
    print(f"TARGET_LISTS: {TARGET_LISTS}")
    # COMMIT_MSG_REGEX = r'^\[(' + '|'.join(TARGET_LISTS) + r')\](feat|fix|refactor|docs|style|test|chore)(\([\w\-]+\))?: .{1,50}'
    COMMIT_MSG_REGEX = COMMIT_MSG_REGEX_TEMPLATE.format(targets='|'.join(TARGET_LISTS))


def read_config(config_file):
    config = configparser.ConfigParser()
    config.read(config_file)

    # 检查各section中的required options
    required_options = {
        "LOG": ["log_file_path"],
        "CONFIG": ["target_file_path"]
    }
    missing_options = []
    for section, opts in required_options.items():
        if not config.has_section(section):
            missing_options.extend([f"{section}.{opt}" for opt in opts])
        else:
            missing_options.extend([f"{section}.{opt}" for opt in opts if not config.has_option(section, opt)])
    
    if missing_options:
        raise ValueError(f"Missing required options in config file: {', '.join(missing_options)}")
    

    return {
        "log_file_path": config.get("LOG", "log_file_path"),
        "target_file_path": config.get("CONFIG", "target_file_path")
    }

def write_config(config_file):
    config = configparser.ConfigParser()
    config.read(config_file)

    required_options = ["fw_type"]
    missing_options = [opt for opt in required_options if not config.has_option("PROJECT_VER", opt)]
    
    if missing_options:
        raise ValueError(f"Missing required options in config file: {', '.join(missing_options)}")
    
    # 写入新的值到 fw_type
    config.set("PROJECT_VER", "fw_type", "release")
    
    # 将修改后的配置写回文件
    with open(config_file, 'w') as configfile:
        config.write(configfile)

    return {
        "fw_type": config.get("PROJECT_VER", "fw_type")
    }

def read_commit_message(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        return file.read().strip()

def check_commit_format(commit_msg):
    match = re.match(COMMIT_MSG_REGEX, commit_msg)
    if not match:
        # Detailed error message
        program_match = re.match(r'^\[(.*?)\]', commit_msg)
        if not program_match or program_match.group(1) not in TARGET_LISTS:
            return False, "Program identifier is incorrect or missing."
        
        type_match = re.match(r'^\[.*?\](feat|fix|refactor|docs|style|test|chore)', commit_msg)
        if not type_match:
            return False, "Commit type is incorrect or missing."
        
        scope_match = re.match(r'^\[.*?\](feat|fix|refactor|docs|style|test|chore)(\([\w\-]+\))?', commit_msg)
        if not scope_match:
            return False, "Commit scope is incorrect or missing."
        
        message_match = re.match(r'^\[.*?\](feat|fix|refactor|docs|style|test|chore)(\([\w\-]+\))?: .{1,50}', commit_msg)
        if not message_match:
            return False, "Commit message is incorrect or missing."
    
    return True, "Commit message format is correct."

if __name__ == "__main__":
    # 文件路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # print(f"script_dir: {script_dir}")  # 添加调试信息
    project_dir = os.path.dirname(script_dir)
    print(f"project dir: {project_dir}")  # 添加调试信息

    config_path = os.path.join(script_dir, 'config.ini')
    print(f"config path: {config_path}")  # 添加调试信息

    GENERATE_LOG_SCRIPT_FILE_NAME = os.path.join(script_dir, "EE_generate_changeLog.py")

    # 读取项目版本默认参数
    config = read_config(config_path)

    # 
    log_file_path = os.path.abspath(os.path.join(project_dir, config["log_file_path"]))
    print(f"log_file_path: {log_file_path}")  # 添加调试信息

    # 更新TARGET_LISTS
    eide_file_path = os.path.join(project_dir, config["target_file_path"]) #".eide", 'eide.json')
    print(f"eide.json path: {eide_file_path}")  # 添加调试信息

    update_targets(eide_file_path)

    commit_message = read_commit_message(COMMIT_MSG_FILE)

    # 先检查 commit msg是否符合规范，有异常直接退出
    # is_valid, error_message = check_commit_format(commit_message)
    # if not is_valid:
    #     print(f"Commit message is incorrect: {error_message}")
    #     # print(f"Invalid commit message: {commit_message}")
    #     sys.exit(1)
    
    # Organize commit messages by program
    commit_dict = {program: [] for program in TARGET_LISTS}
    for line in commit_message.split('\n'):
        # 检查 commit msg是否符合规范，有异常直接退出
        is_valid, error_message = check_commit_format(line)
        if not is_valid:
            print(f"Commit message is incorrect: {error_message}")
            print(f"Invalid commit message: {line}")
            sys.exit(1)

        for program in TARGET_LISTS:
            if f"[{program}]" in line or f"[{TARGET_LISTS[0]}]" in line:
                only_message = re.sub(r'^\[.*?\]', '', line).strip() #去除program 名称
                commit_dict[program].append(only_message)
                print(f"{program} cache messages : {only_message}")
                # break

    for program, messages in commit_dict.items():
        if messages:
            if program != TARGET_LISTS[0]: 
                program = os.path.join(log_file_path, program)
            else: 
                program = log_file_path
            program_directory = os.path.join(os.getcwd(), program)
            if not os.path.exists(program_directory):
                print(f"Program directory {program_directory} does not exist. Creating directory.")
                os.makedirs(program_directory)
                # sys.exit(1)
            print(f"Commit messages for {program}: {messages}")
            subprocess.run([sys.executable, GENERATE_LOG_SCRIPT_FILE_NAME, program_directory] + messages, check=True)
    # write_config(config_path)        
    sys.exit(0)
