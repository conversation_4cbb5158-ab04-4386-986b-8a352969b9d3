#ifndef __SPEED_CONTROL_H__
#define __SPEED_CONTROL_H__

#include <stdint.h>

typedef struct {
    volatile uint8_t isSpeedStabled : 1;        // ת���ȶ���ʶ
    volatile uint8_t isDetectStartEncoder : 1;  // ��⵽��С�ݱ�־
    volatile uint8_t isStalled : 1;             // ��ת��־
    volatile uint8_t isShowFont : 1;            // ��ʾ�����־
    volatile uint8_t isHighSpeed : 1;           // ���ٱ�־
    volatile uint8_t isMediumSpeed : 1;         // ���ٱ�־
    volatile uint8_t isLowSpeed : 1;            // ���ٱ�־
    volatile uint8_t isTrackAngle : 1;          // �Ƕ�׷��

    volatile uint8_t isDetectSpeed : 1;    // �������ٶ��ܱ�ʶ(һȦ�ж�һ��)
    volatile uint8_t isFinHighConfig : 1;  // Ԥ��
    volatile uint8_t isFinMidConfig : 1;   // Ԥ��
    volatile uint8_t isFinLowConfig : 1;
    volatile uint8_t isFinTrackConfig : 1;
    volatile uint8_t isSendStabled : 1;
    volatile uint8_t isReserved2 : 1;
    volatile uint8_t isReserved3 : 1;

    int16_t currentSpeed;     // ��ǰ�ٶ�
    int16_t currentSpeedPre;  // ��ǰ�ٶȵ���һ���ٶ�

    uint8_t speedFlag;
    uint8_t speedCnt;
    uint8_t speedFlagPre;
    uint8_t speedStableFlag;
    uint8_t motorOK;
} PtrRegSpeed;
extern PtrRegSpeed g_regSpeed;


#endif /* __SPEED_CONTROL_H__ */
