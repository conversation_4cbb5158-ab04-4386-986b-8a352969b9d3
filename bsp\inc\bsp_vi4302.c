/*
 * @Author: xx
 * @Date: 2024-05-21 20:06:29
 * @LastEditors: Do not edit
 * @LastEditTime: 2024-08-05 18:15:59
 * @Description: 
 * @FilePath: \MDK-ARMc:\Users\<USER>\Desktop\fw01_dToF_lidar\Bsp\src\bsp_vi4302.c
 */
#include "bsp_vi4302.h"
#include "bsp_adc.h"
#include "bsp_io.h"
#include "systick.h"
// #include "variable_table.h"



const uint16_t vi4302Register[][2] = {

#if USING_BUFF_DRIVER != 1
    0x0230, 0xF1,  // qch(�������) �� ck������
    0x0235, 0x3F,  // qch(�������) �� ck������ ֵ
    0x0209, 0x50,  // tx trigger �ֵ�
    0x0243, 0x02,  // tx trigger ����
#if (defined A2_D4_SAMSUNG) || (defined A2_D4_POSITIC) ||    \
    (defined A2_D4A_UMOUSE) || (defined A2_T4_JINGCHUANG) || \
    (defined A2_T4_WEILAN) || (defined A2_T4_TAKDIR) ||      \
    (defined A2_T4_NARWAL) || (defined A2_T4A_MEELUX) || (defined A2_D6_XX) || (defined A2_T4B_XX) || (defined A2_NA4_XX) || \
    (defined A2_T5_XX)
    0x0250, 0x02,  // ����enable �ڲ�dcdc
#endif

    0x0242, 0x00, 
    0x024E, 0x81, 
    0x0231, 0xFF,
    0x0232, 0xFF, 
    0x0233, 0xFF, 
    0x0234, 0x01,  // MP [0-24] enable
    0x00d3, 0x01,  // ʹ��SPI �ⲿ�ж�
    0x00d4, 0x04,  // ����FIFO�����
    0x00d5, 0x01,  // ��FIFO�Ѿ���ʱ�򣬶����µ�����
    0x00b4, 0x21,  // MA����[1:0]
    0x00b3, 0x43,  // MA����[3:2]
    0x00b2, 0x65,  // MA����[5:4]
    0x00b1, 0x87,  // MA����[7:6]
    0x00b0, 0x67,  // MA����[9:8]
    0x00af, 0x45,  // MA����[11:10]
    0x00ae, 0x23,  // MA����[13:12]
    0x00ad, 0x01,  // MA����[15:14]

    0x00e5, 0x01,                // ����������εݼ�(OP2)
    0x0080, 0x00, 
    0x0081, 0x96,  // ���ü�����ƵΪ1M
    0x0086, 0x00, 
    0x0087, 175,   // ���û���Ϊ150
#else
    0x0230, 0xF0,  // qch(�������) �� ck������
    0x0235, 0x44,  // qch(�������) �� ck������ ֵ
    0x0209, 0x10,  // tx trigger �ֵ�
    0x0243, 0x0E,  // tx trigger ����
#if (defined A2_D4_SAMSUNG) || (defined A2_D4_POSITIC) ||    \
    (defined A2_D4A_UMOUSE) || (defined A2_T4_JINGCHUANG) || \
    (defined A2_T4_WEILAN) || (defined A2_T4_TAKDIR) ||      \
    (defined A2_T4_NARWAL) || (defined A2_T4A_MEELUX) || (defined A2_D6_XX) || (defined A2_T4B_XX) || (defined A2_NA4_XX) || \
    (defined A2_T5_XX)
    0x0250, 0x02,  // ����enable �ڲ�dcdc
#endif
    0x0242, 0x00, 
    0x024E, 0x81, 
    0x0231, 0xFF,
    0x0232, 0xFF, 
    0x0233, 0xFF, 
    0x0234, 0x01,  // MP [0-24] enable
    0x00d3, 0x01,  // ʹ��SPI �ⲿ�ж�
    0x00d4, 0x04,  // ����FIFO�����
    0x00d5, 0x01,  // ��FIFO�Ѿ���ʱ�򣬶����µ�����
    0x00b4, 0x21,  // MA����[1:0]
    0x00b3, 0x43,  // MA����[3:2]
    0x00b2, 0x65,  // MA����[5:4]
    0x00b1, 0x87,  // MA����[7:6]
    0x00b0, 0x67,  // MA����[9:8]
    0x00af, 0x45,  // MA����[11:10]
    0x00ae, 0x23,  // MA����[13:12]
    0x00ad, 0x01,  // MA����[15:14]

    0x00e5, 0x01,  // ����������εݼ�(OP2)
    0x0080, 0x00, 
    0x0081, 0x96,  // ���ü�����ƵΪ1M
    0x0086, 0x00, 
    0x0087, 175,   // ���û���Ϊ150

#endif

};

const uint16_t vi4302RangeRegister[][2] = {

/***********/
#if USING_BUFF_DRIVER != 1
    0x206, 0x40,  // ref
    0X20B, 0x04, 
    0x0209, 0x50, // tx trigger �ֵ� ����ʵ���������
    0x0243, 0x02, // tx trigger ���� ����ʵ���������
    0x0242, 0x00, // CK phase

    /*0x020C,0xFF,
    0x020D,0xFF,
    0x020E,0xFF,
    0x020F,0xFF,
    0x0210,0xFF,
    0x0211,0xFF,
    0x0212,0xFF,
    0x0213,0xFF,
    0x0214,0xFF,
    0x0215,0xFF,
    0x0216,0xFF,
    0x0217,0xFF,
    0x0218,0xFF,
    0x0219,0xFF,
    0x021A,0xFF,
    0x021B,0xFF,
    0x021C,0xFF,
    0x021D,0xFF,
    0x021E,0xFF,
    0x021F,0xFF,
    0x0220,0xFF,
    0x0221,0xFF,
    0x0222,0xFF,
    0x0223,0xFF,
    0x0224,0xFF,
    0x0225,0xFF,
    0x0226,0xFF,
    0x0227,0xFF,
    0x0228,0xFF,
    0x0229,0xFF,
    0x022A,0xFF,
    0x022B,0xFF,
    0x022C,0xFF,
    0x022D,0xFF,
    0x022E,0xFF,
    0x022F,0xFF,*/

    // spad
    0x020C, 0x10, 
    0x020D, 0xFE, 
    0x020E, 0xFF, 
    0x020F, 0xFF, 
    0x0210, 0xFF,
    0x0211, 0xFF, 
    0x0212, 0x0F, 
    0x0213, 0xE1, 
    0x0214, 0xFF, 
    0x0215, 0xFF,
    0x0216, 0xFF, 
    0x0217, 0xFF, 
    0x0218, 0xFF, 
    0x0219, 0xFF, 
    0x021A, 0xFF,
    0x021B, 0xFF, 
    0x021C, 0xFF, 
    0x021D, 0xFF, 
    0x021E, 0xFF, 
    0x021F, 0xFF,
    0x0220, 0xFF, 
    0x0221, 0xFF, 
    0x0222, 0xFF, 
    0x0223, 0xFF, 
    0x0224, 0xFF,
    0x0225, 0xFF, 
    0x0226, 0xFF, 
    0x0227, 0x1F, 
    0x0228, 0xC2, 
    0x0229, 0xFF,
    0x022A, 0xFF, 
    0x022B, 0xFF, 
    0x022C, 0xFF, 
    0x022D, 0xFF,    
    0x022E, 0x21,
    0x022F, 0xFC,

    0x0047, 0x17, 
    0x0048, 0x17, 
    0x0049, 0x17, 
    0x004A, 0x1F, 
    0x004B, 0x1F,

    0x0230, 0xF1,  // qch(�������) �� ck������
    0x0231, 0xFF, 
    0x0232, 0xFF, 
    0x0233, 0xFF, 
    0x0234, 0x01,  // MP [0-24] enable 5*3 + 4
    #if (defined A0_D4_SAMSUNG) || (defined A2_D4_SAMSUNG) || (defined A0_D4_POSITIC) || (defined A2_D4_POSITIC) ||  \
        (defined A0_D4A_UMOUSE) || (defined A2_D4A_UMOUSE)

    0x0235, 0x2F,  // ch(�������) �� ck������ ֵ
    #elif (defined A0_D6_XX) || (defined A2_D6_XX)
    0x0235, 0x3A,  // ch(�������) �� ck������ ֵ
    #elif (defined A0_T4B_XX) || (defined A2_T4B_XX)
    0x0235, 0x2F,
    #elif (defined A0_T4_JINGCHUANG) || (defined A2_T4_JINGCHUANG) || (defined A0_T4_WEILAN) || (defined A2_T4_WEILAN) || \
			(defined A0_T4_TAKDIR) || (defined A2_T4_TAKDIR) || (defined A0_T4_NARWAL) || (defined A2_T4_NARWAL) || \
			(defined A0_NA4_XX) || (defined A2_NA4_XX) || (defined A0_T4A_MEELUX) || (defined A2_T4A_MEELUX) || (defined A0_T5_XX) || (defined A2_T5_XX)
    0x0235, 0x2F,  // ch(�������) �� ck������ ֵ
    #else
    0x0235, 0x3F,  // ch(�������) �� ck������ ֵ
    #endif

    0x0080, 0x00, 
    0x0081, 0x4B,  // ���ü�����ƵΪ2M
    0x0086, 0x00, 
    0x0087, 175,   // ���û���Ϊ150
#else
    0x206, 0x40, 
    0x20B, 0x04, 
    0x0209, 0x10,  // tx trigger �ֵ� ����ʵ���������
    0x0243, 0x0E,  // tx trigger ���� ����ʵ���������
    0x0242, 0x00,  // CK phase

    0x020C, 0x10, 
    0x020D, 0xFE, 
    0x020E, 0xFF, 
    0x020F, 0xFF, 
    0x0210, 0xFF,
    0x0211, 0xFF, 
    0x0212, 0x0F, 
    0x0213, 0xE1, 
    0x0214, 0xFF, 
    0x0215, 0xFF,
    0x0216, 0xFF, 
    0x0217, 0xFF, 
    0x0218, 0xFF, 
    0x0219, 0xFF, 
    0x021A, 0xFF,
    0x021B, 0xFF, 
    0x021C, 0xFF, 
    0x021D, 0xFF, 
    0x021E, 0xFF, 
    0x021F, 0xFF,
    0x0220, 0xFF, 
    0x0221, 0xFF, 
    0x0222, 0xFF, 
    0x0223, 0xFF, 
    0x0224, 0xFF,
    0x0225, 0xFF, 
    0x0226, 0xFF, 
    0x0227, 0x1F, 
    0x0228, 0xC2, 
    0x0229, 0xFF,
    0x022A, 0xFF, 
    0x022B, 0xFF, 
    0x022C, 0xFF, 
    0x022D, 0xFF, 
    0x022E, 0x21,
    0x022F, 0xFC,

    0x0047, 0x17, 
    0x0048, 0x13, 
    0x0049, 0x13, 
    0x004A, 0x1B, 
    0x004B, 0x1F,

    /*0x0047,0x17,
    0x0048,0x17,
    0x0049,0x17,
    0x004A,0x1F,
    0x004B,0x1F,*/

    /*0x0047,0x1F,
    0x0048,0x1B,
    0x0049,0x1B,
    0x004A,0x1B,
    0x004B,0x1F,*/

    0x0230, 0xF1,  // qch(�������) �� ck������
    0x0231, 0xFF, 
    0x0232, 0xFF, 
    0x0233, 0xFF, 
    0x0234, 0x01,  // MP [0-24] enable 5*3 + 4
    #if (defined A0_D4_SAMSUNG) || (defined A2_D4_SAMSUNG) || (defined A0_D4_POSITIC) || (defined A2_D4_POSITIC) ||  \
        (defined A0_D4A_UMOUSE) || (defined A2_D4A_UMOUSE)

    0x0235, 0x2F,  // ch(�������) �� ck������ ֵ
    #elif (defined A0_D6_XX) || (defined A2_D6_XX)
    0x0235, 0x3A,  // ch(�������) �� ck������ ֵ
    #elif (defined A0_T4B_XX) || (defined A2_T4B_XX)
    0x0235, 0x2F,
    #elif (defined A0_T4_JINGCHUANG) || (defined A2_T4_JINGCHUANG) || (defined A0_T4_WEILAN) || (defined A2_T4_WEILAN) || \
			(defined A0_T4_TAKDIR) || (defined A2_T4_TAKDIR) || (defined A0_T4_NARWAL) || (defined A2_T4_NARWAL) || \
			(defined A0_NA4_XX) || (defined A2_NA4_XX) || (defined A0_T4A_MEELUX) || (defined A2_T4A_MEELUX) || (defined A0_T5_XX) || (defined A2_T5_XX)
    0x0235, 0x2F,  // ch(�������) �� ck������ ֵ
    #else
    0x0235, 0x3F,  // ch(�������) �� ck������ ֵ
    #endif

    0x0080, 0x00, 
    0x0081, 0x4B,  // ���ü�����ƵΪ2M
    0x0086, 0x00, 
    0x0087, 200,   // ���û���Ϊ150

#endif
};

uint8_t Vi4302FaculaRegisterInit(uint8_t type) {
    uint8_t configRegCnt = 0;
    uint16_t gm_size = sizeof(vi4302Register) / sizeof(vi4302Register[0]);
    uint16_t range_size =
        sizeof(vi4302RangeRegister) / sizeof(vi4302RangeRegister[0]);
    if (type == kSinglePixel) {
        for (uint8_t m = 0; m < gm_size; m++) {
            configRegCnt += g_ptrSpi.wRegister(vi4302Register[m][0],
                                               vi4302Register[m][1], NULL);
        }
        if (configRegCnt == gm_size) {
            configRegCnt = 1;
        }
    } else if (type == kRanging) {
        for (uint8_t m = 0; m < range_size; m++) {
            configRegCnt += g_ptrSpi.wRegister(vi4302RangeRegister[m][0],
                                               vi4302RangeRegister[m][1], NULL);
        }
        if (configRegCnt == range_size) {
            configRegCnt = 1;
        }
    }

    return configRegCnt;
}

int8_t Vi4302SelectFrame(uint8_t frame, uint8_t enable) {
    uint8_t value = 0;
    // clear frame fifo
    value = SET_8BIT(0);
    g_ptrSpi.wRegister(0xd6, value, NULL);
    // select frame output
    g_ptrSpi.rRegister(0x8c, &value, NULL);
    if (frame == kSingleFrame) {
        value &= (CLEAR_8BIT(4) | CLEAR_8BIT(1));  // ��single��mult�ı�־�����
        value |= enable << 1;
    } else if (frame == kMultFrame) {
        value &= CLEAR_8BIT(0);  // ��single��־���
        value |= SET_8BIT(4) | (enable << 0);
    } else {
        return -1;
    }

    g_ptrSpi.wRegister(0x8c, value, NULL);

    return 1;
}

int8_t Vi4302SetFrameFormat(uint8_t mpType,
                            uint8_t tofChannel,
                            uint8_t peakChannel,
                            uint8_t multshotNoiseChannel) {
    int8_t status = -1;
    uint8_t value = 0;

    if (mpType == kNormalMp) {
        // tof
        g_ptrSpi.rRegister(0xd8, &value, NULL);
        value &= CLEAR_8BITS(0, 3);
        value |= (tofChannel & 0x0F);
        g_ptrSpi.wRegister(0xd8, value, NULL);

        // peak
        value = 0;
        g_ptrSpi.rRegister(0xda, &value, NULL);
        value &= CLEAR_8BITS(0, 3);
        value |= (peakChannel & 0x0F);
        g_ptrSpi.wRegister(0xda, value, NULL);

        // noise and multshot
        value = 0;
        g_ptrSpi.rRegister(0xdc, &value, NULL);
        value &= CLEAR_8BIT(0);
        value &= CLEAR_8BIT(2);
        value |= (multshotNoiseChannel & 0x01) | (multshotNoiseChannel & 0x02)
                                                     << 1;
        g_ptrSpi.wRegister(0xdc, value, NULL);

    } else if (mpType == kAttenuation) {
        // tof
        g_ptrSpi.rRegister(0xd8, &value, NULL);
        value &= CLEAR_8BITS(4, 7);
        value |= (tofChannel & 0x0F) << 4;
        g_ptrSpi.wRegister(0xd8, value, NULL);

        // peak
        value = 0;
        g_ptrSpi.rRegister(0xda, &value, NULL);
        value &= CLEAR_8BITS(4, 7);
        value |= (peakChannel & 0x0F) << 4;
        g_ptrSpi.wRegister(0xda, value, NULL);

        // noise and multshot
        value = 0;
        g_ptrSpi.rRegister(0xdc, &value, NULL);
        value &= CLEAR_8BIT(1);
        value &= CLEAR_8BIT(3);
        value |= (multshotNoiseChannel & 0x01) << 1 |
                 (multshotNoiseChannel & 0x02) << 2;
        g_ptrSpi.wRegister(0xdc, value, NULL);
    } else if (mpType == kReference) {
        // tof
        g_ptrSpi.rRegister(0xd9, &value, NULL);
        value &= CLEAR_8BIT(0);
        value |= (tofChannel & 0x01);
        g_ptrSpi.wRegister(0xd9, value, NULL);

        // peak
        value = 0;
        g_ptrSpi.rRegister(0xdb, &value, NULL);
        value &= CLEAR_8BIT(0);
        value |= (peakChannel & 0x01);
        g_ptrSpi.wRegister(0xdb, value, NULL);
    } else {
        status = -1;
    }

    return status;
}

int8_t Vi4302MpOpen(uint8_t index) {
    uint16_t addr = 0x231;
    uint8_t value = 0;

    addr += index / 8;
    value = 0x01 << (index % 8);

    g_ptrSpi.wRegister(0x231, 0x00, NULL);
    g_ptrSpi.wRegister(0x232, 0x00, NULL);
    g_ptrSpi.wRegister(0x233, 0x00, NULL);
    g_ptrSpi.wRegister(0x234, 0x00, NULL);
    g_ptrSpi.wRegister(addr, value, NULL);

    return 1;
}

uint8_t Vi4300GetFrameCnt(void) {
    uint8_t value = 0;
    g_ptrSpi.rRegister(0xdd, &value, NULL);
    return value;
}

int8_t Vi4300GetMpPeak(uint8_t* buff, uint16_t startAddr, uint8_t size) {
    int8_t status = -1;
    if (size != 4) {
        return -1;
    }
    uint8_t tBuff[3 + 4] = {0};
    tBuff[0] = SPI_4302_REG_READ;
    tBuff[1] = startAddr >> 8;
    tBuff[2] = startAddr;
    status = g_ptrSpi.SpiReadBytes(tBuff, 3 + size, buff, 3 + size);

    return status;
}

int Vi4302McuFirmwareLoad(void) {
    uint8_t value = 0, r_val = 0;
    ;
    static uint32_t addr = 0;
    uint8_t timeOutCnt = 0;
    uint16_t firmLeninteger = 0;
    uint16_t firmLendecimal = 0;

    firmLeninteger = get_fw_bytes() / 256;
    firmLendecimal = get_fw_bytes() % 256;

    // clear firmware state
    g_ptrSpi.wRegister(0x30, 0, NULL);

    // mcu reset release system reset  and  boot from ram
    g_ptrSpi.rRegister(0x01, &value, NULL);

    value &= CLEAR_8BIT(1);
    g_ptrSpi.wRegister(0x01, value, NULL);

    g_ptrSpi.wRegister(0x02, 0, NULL);

    // unlock firmware
    value = 0x06;
    // g_ptrSpi.SpiWriteBytes(&value,1);
    g_ptrSpi.SpiReadBytes(&value, 1, &r_val, 1);

    // load firmware
    for (uint16_t i = 0; i < firmLeninteger; i++) {
        memcpy(&cache[3], &VI4300_FIRMWARE[i * 256], 256);
        cache[0] = SPI_4302_LOAD_FIRMWARE;
        cache[1] = (addr >> 8);
        cache[2] = (addr);
        g_ptrSpi.SpiReadBytes(cache, 256 + 3, cache, 256 + 3);
        addr += 256;
        memset(cache, 0, 256 + 3);
    }

    memcpy(&cache[3], &VI4300_FIRMWARE[firmLeninteger * 256], firmLendecimal);
    cache[0] = SPI_4302_LOAD_FIRMWARE;
    cache[1] = (addr >> 8);
    cache[2] = (addr);
    g_ptrSpi.SpiReadBytes(cache, firmLendecimal + 3, cache, firmLendecimal + 3);

    // g_ptrSpi.SpiWriteBytes((uint8_t
    // *)VI4300_FIRMWARE,sizeof(VI4300_FIRMWARE));

    // lock firmware
    value = 0x07;
    g_ptrSpi.SpiReadBytes(&value, 1, &r_val, 1);

    // mcu reset release system reset  and  boot from ram
    g_ptrSpi.rRegister(0x01, &value, NULL);
    value |= SET_8BIT(1);

    g_ptrSpi.wRegister(0x01, value, NULL);

    while (1) {
        g_ptrSpi.rRegister(0x30, &value, NULL);
        timeOutCnt++;
        delay_1ms(1);
        if (timeOutCnt > 100) {
            return -1;
        }

        if (value == SPI_4302_FIRMWARE_SUCCESS) {
            return 1;
        }
    }

    // return 1;
}

int Vi4302McuFirmwareHistLoad(void) {
    //	uint8_t value = 0,r_val=0;;
    //	static uint32_t  addr = 0;
    //	uint16_t firmLeninteger = 0;
    //	uint16_t firmLendecimal = 0;
    //	firmLeninteger = sizeof(VI4300_FIRMWARE_HIST)/256;
    //	firmLendecimal = sizeof(VI4300_FIRMWARE_HIST)%256;
    //
    //
    //	// clear firmware state
    //	g_ptrSpi.wRegister(0x30,0,NULL);
    //
    //	//mcu reset release system reset  and  boot from ram
    //	g_ptrSpi.rRegister(0x01,&value,NULL);
    //
    //	value &= CLEAR_8BIT(1);
    //	g_ptrSpi.wRegister(0x01,value,NULL);
    //
    //	g_ptrSpi.wRegister(0x02,0,NULL);
    //
    //	//unlock firmware
    //	value = 0x06;
    //	//g_ptrSpi.SpiWriteBytes(&value,1);
    //	g_ptrSpi.SpiReadBytes(&value,1,&r_val,1);
    //
    //	//load firmware
    //	for(uint16_t i = 0;i < firmLeninteger;i++)
    //	{
    //		 memcpy(&cache[3],&VI4300_FIRMWARE_HIST[i*256],256);
    //		 cache[0] = SPI_4302_LOAD_FIRMWARE;
    //		 cache[1] = (addr >> 8);
    //		 cache[2]= (addr);
    //		 g_ptrSpi.SpiReadBytes(cache,256+3,cache,256+3);
    //		 addr += 256;
    //		 memset(cache,0,256+3);
    //	}

    //	memcpy(&cache[3],&VI4300_FIRMWARE_HIST[firmLeninteger*256],firmLendecimal);
    //	cache[0] = SPI_4302_LOAD_FIRMWARE;
    //	cache[1] = (addr >> 8);
    //	cache[2]= (addr);
    //	g_ptrSpi.SpiReadBytes(cache,firmLendecimal+3,cache,firmLendecimal+3);
    //
    //	//g_ptrSpi.SpiWriteBytes((uint8_t
    //*)VI4300_FIRMWARE,sizeof(VI4300_FIRMWARE));

    //	//lock firmware
    //	value = 0x07;
    //	g_ptrSpi.SpiReadBytes(&value,1,&r_val,1);
    //
    //	//mcu reset release system reset  and  boot from ram
    //	g_ptrSpi.rRegister(0x01,&value,NULL);
    //	value |= SET_8BIT(1);
    //
    //	g_ptrSpi.wRegister(0x01,value,NULL);
    //
    //	while(1) {
    //		g_ptrSpi.rRegister(0x30,&value,NULL);
    //
    //		if(value == SPI_4302_FIRMWARE_SUCCESS) {
    //			return 1;
    //		}
    //	}

    return 1;
}

int8_t VI4302_Temp_Bvd(void) {
    //	uint8_t value = 0,rValue = 0;
    //	g_ptrSpi.wRegister(0x30,0x00,NULL);
    //
    //	g_ptrSpi.wRegister(0x31,(uint8_t)(g_ptrSysParam.currentTemperature +
    // 0.5f),NULL); 	g_ptrSpi.wRegister(0x32,(uint8_t)(g_ptrSysParam.vbdTmp -
    // 30),NULL); 	g_ptrSpi.wRegister(0x33,g_ptrSysParam.vbdStep,NULL);
    // value = 0x19 + 0x10; 	g_ptrSpi.SpiReadBytes(&value,1,&rValue,1);

    /*g_ptrSpi.rRegister(0x24f,&value,NULL);
    printf("addr 0x247 %02x",value);*/

    uint8_t value = 0, rValue = 0;
    uint8_t ts = (uint8_t)(g_ptrSysParam.currentTemperature + 0.5f);
    uint8_t vbd_ts = (uint8_t)(g_ptrSysParam.vbdTmp - 30);
    uint8_t check = ~(ts + vbd_ts + g_ptrSysParam.vbdStep + 0x29);
    g_ptrSpi.wRegister(0x30, 0x00, NULL);

    g_ptrSpi.wRegister(0x31, ts, NULL);
    g_ptrSpi.wRegister(0x32, vbd_ts, NULL);
    g_ptrSpi.wRegister(0x33, g_ptrSysParam.vbdStep, NULL);
    g_ptrSpi.wRegister(0x34, 0, NULL);
    g_ptrSpi.wRegister(0x35, check, NULL);
    value = 0x19 + 0x10;
    g_ptrSpi.SpiReadBytes(&value, 1, &rValue, 1);

    return 1;
}

uint8_t VbdAutoCalculateByCmd() {
    uint8_t value = 0, rValue = 0;
    uint16_t timeOutCnt = 0;

    g_ptrSpi.wRegister(0x30, 0x00, NULL);
    g_ptrSpi.wRegister(0x31, 0x8F, NULL);
    g_ptrSpi.wRegister(0x32, 0x1E, NULL);
    g_ptrSpi.wRegister(0x33, 0x17, NULL);
    g_ptrSpi.wRegister(0x34, 0x00, NULL);
    value = 0x02 + 0x10;
    g_ptrSpi.SpiReadBytes(&value, 1, &rValue, 1);

    while (1) {
        g_ptrSpi.rRegister(0x30, &value, NULL);

        timeOutCnt++;
        delay_1ms(1);
        if (timeOutCnt > 1000) {
            return 0;
        }

        if (value == 0x11) {
            g_ptrSpi.rRegister(0x31, &value, NULL);

            g_ptrSysFlag.isGpioTrig0 = false;
            return value;
        }
    }
}

int8_t VbdAutoCalculate(uint8_t* step) {
    uint8_t value = 0;

    // g_ptrSpi.rRegister(0x24F,&value,NULL);

    value = VbdAutoCalculateByCmd() + 1;  // r003�ڲ��̼�vbd����+12��

    if (value < 0xCE || value > 0xFB) {
        *step = 1;
        return -1;
    } else {
        *step = value;
        return 1;
    }
}

int8_t tdcAutoCalculate(uint8_t* tdc) {
    uint8_t value = 0, rValue = 0;
    uint16_t timeOutCnt = 0;

    g_ptrSpi.wRegister(0x30, 0x00, NULL);
    //	g_ptrSpi.wRegister(0x31,0x0B,NULL);
    //	g_ptrSpi.wRegister(0x32,0x0C,NULL);
    g_ptrSpi.wRegister(0x31, 0x0A, NULL);
    g_ptrSpi.wRegister(0x32, 0x0B, NULL);
    value = 0x14 + 0x10;
    g_ptrSpi.SpiReadBytes(&value, 1, &rValue, 1);

    while (1) {
        g_ptrSpi.rRegister(0x30, &value, NULL);

        timeOutCnt++;
        delay_1ms(1);
        if (timeOutCnt > 1000) {
            return -1;
        }

        if (value == 0x11) {
            g_ptrSpi.rRegister(0x31, &value, NULL);
            *tdc = value;
            g_ptrSysFlag.isGpioTrig0 = false;
            return 1;
        }
    }
}

int8_t FrameSetting(uint16_t fps, uint16_t* ackFps) {
    uint8_t value = 0, rValue = 0;
    uint16_t timeOutCnt = 0;

    fps -= 15;
    g_ptrSpi.wRegister(0x30, 0x00, NULL);
    g_ptrSpi.wRegister(0x31, fps, NULL);
    g_ptrSpi.wRegister(0x32, fps >> 8, NULL);
    value = 0x0B + 0x10;
    g_ptrSpi.SpiReadBytes(&value, 1, &rValue, 1);

    while (1) {
        g_ptrSpi.rRegister(0x30, &value, NULL);

        timeOutCnt++;
        delay_1ms(1);
        if (timeOutCnt > 1000) {
            g_ptrSysFlag.errorCode |= SET_16BIT(9);
            return -1;
        }

        if (value == 0x11) {
            g_ptrSpi.rRegister(0x31, &value, NULL);
            (*ackFps) = value;
            g_ptrSpi.rRegister(0x32, &value, NULL);
            *ackFps = *ackFps | value << 8;

            g_ptrSysFlag.isGpioTrig0 = false;
            return 1;
        }
    }
}

int8_t Vi4302StartRanging(void) {
    uint8_t value = 0, rValue = 0;
    uint8_t timeOutCnt = 0;

    g_ptrSpi.wRegister(0x31, 0x01, NULL);
    g_ptrSpi.wRegister(0x32, 0x02, NULL);  // fw007 �����
    g_ptrSpi.wRegister(0x33, 0x02, NULL);
    value = 0x09 + 0x10;
    g_ptrSpi.SpiReadBytes(&value, 1, &rValue, 1);

    while (1) {
        g_ptrSpi.rRegister(0x30, &value, NULL);

        timeOutCnt++;
        delay_1ms(1);
        if (timeOutCnt > 100) {
            g_ptrSysFlag.errorCode |= SET_16BIT(3);
            return -1;
        }

        if (value == 0x11) {
            g_ptrSysFlag.isGpioTrig0 = false;
            return 1;
        }
    }
}

int8_t Vi4302StopRanging(void) {
    uint8_t value = 0, rValue = 0;
    uint8_t timeOutCnt = 0;
    // stop ranging
    g_ptrSpi.wRegister(0x31, 0x00, NULL);
    g_ptrSpi.wRegister(0x32, 0x00, NULL);
    g_ptrSpi.wRegister(0x33, 0x01, NULL);
    value = 0x09 + 0x10;
    g_ptrSpi.SpiReadBytes(&value, 1, &rValue, 1);

    while (1) {
        g_ptrSpi.rRegister(0x30, &value, NULL);

        timeOutCnt++;
        delay_1ms(1);
        if (timeOutCnt > 100) {
            g_ptrSysFlag.errorCode |= SET_16BIT(3);
            return -1;
        }

        if (value == 0x11) {
            g_ptrSysFlag.isGpioTrig0 = false;
            break;
        }
    }
    return 1;
}

int8_t Vi4302StartHist(hist_channel channel) {
    uint8_t value = 0, rValue = 0;
    uint8_t timeOutCnt = 0;

    // setting normal hist format
    g_ptrSpi.wRegister(0x20A, 0x02, NULL);  // enable tdc clock
    g_ptrSpi.wRegister(0x31, channel, NULL);
    // setting ref hist format
    // g_ptrSpi.wRegister(0x31,0x13,NULL);
    value = 0x08 + 0x10;
    g_ptrSpi.SpiReadBytes(&value, 1, &rValue, 1);

    while (1) {
        g_ptrSpi.rRegister(0x30, &value, NULL);

        timeOutCnt++;
        delay_1ms(1);
        if (timeOutCnt > 100) {
            g_ptrSysFlag.errorCode |= SET_16BIT(3);
            return -1;
        }

        if (value == 0x11) {
            g_ptrSysFlag.isGpioTrig0 = false;
            break;
        }
    }
    return 1;
}

int8_t Vi4302FpsConfig(uint16_t delay) {
    uint8_t value = 0, rValue = 0;
    uint8_t timeOutCnt = 0;

    // configure fps
    g_ptrSpi.wRegister(0x30, 0x00, NULL);
    g_ptrSpi.wRegister(0x31, (uint8_t)delay, NULL);
    g_ptrSpi.wRegister(0x32, delay >> 8, NULL);
    value = 0x18 + 0x10;
    g_ptrSpi.SpiReadBytes(&value, 1, &rValue, 1);

    while (1) {
        g_ptrSpi.rRegister(0x30, &value, NULL);

        timeOutCnt++;
        delay_1ms(1);
        if (timeOutCnt > 100) {
            g_ptrSysFlag.errorCode |= SET_16BIT(3);
            return -1;
        }

        if (value == 0x11) {
            g_ptrSysFlag.isGpioTrig0 = false;
            break;
        }
    }
    return 1;
}

int8_t vbdMultiCalc(void) {
    uint8_t vbdStep = 0;
    uint16_t vbdSum = 0;
    int8_t vbdStatus = -1;

    uint8_t vbdTemp = 0;
    // uint8_t m=0;
    // for(m=0; m<3; m++) {
    vbdStatus = VbdAutoCalculate(&vbdStep);
    //	if(vbdStatus == 1) {
    //		break;
    //	}
    //}
    // vbdSum = vbdStep + 3;
    vbdSum = vbdStep;  // r003�ڲ��̼�vbd����+12��
    if (vbdSum > 0xFF) {
        vbdStep = 0xFF;
    } else {
        vbdStep = vbdSum;
    }

    if (vbdStatus == -1) {
        g_ptrSysFlag.errorCode |= SET_16BIT(1);
        return -1;
    } else {
        vbdStatus = 1;
    }

    g_ptrSysParam.currentTemperature = GettingTemperature(
        GettingAdcValue());  //((float)TEMPERATURE_TABLE[(GettingAdcValue()
                             //- 214)>>1])/10;

    // write vbdStep to flash
    vbdTemp = (uint8_t)(g_ptrSysParam.currentTemperature + 0.5f + 30);
    g_ptrSysParam.vbdTmp = vbdTemp;
    g_ptrSysParam.vbdStep = vbdStep;

    return vbdStatus;
}

void Vi4302ExeVbdTdc(bool is_swith) {
    uint8_t tdcValue = 0;
    int8_t status = -1;
    uint8_t vbdData[4] = {0};

    g_ptrSysParam.vbdStep = 0;
    g_ptrSysParam.vbdTmp = 0;
    // auto calculate vbd
    status = vbdMultiCalc();
    tdcAutoCalculate(&tdcValue);

    if (status == 1) {
        vbdData[0] = g_ptrSysParam.vbdStep;
        vbdData[1] = g_ptrSysParam.vbdTmp;
        vbdData[2] = tdcValue;
        g_ptrFlash.WriteFlashSomeWord(VBD_ADDR, (uint8_t*)vbdData, 4);
        if (is_swith == true) {
            g_ptrFlash.WriteFlashHalfWord(
                LIDAR_MODE_ADDR,
                kTofFaculaMode);  // first toggle to greymap mode
        }
        NVIC_SystemReset();
    }
}

int8_t xtalk_init(xtalk_param_t *param)
{
    uint8_t value = 0, rValue = 0;
    uint8_t timeOutCnt = 0;
	g_ptrSpi.wRegister( 0x31, param->parameter_types&0xFF, NULL);
	g_ptrSpi.wRegister( 0x32, (param->parameter_types>>8)&0xFF, NULL);
	g_ptrSpi.wRegister( 0x35, param->para_MP, NULL);
	g_ptrSpi.wRegister( 0x36, param->xtalk_bin&0xFF, NULL);
	g_ptrSpi.wRegister( 0x37, param->xtalk_peak&0xff, NULL);
	g_ptrSpi.wRegister( 0x38, (param->xtalk_peak>>8)&0xff, NULL);
	g_ptrSpi.wRegister( 0x3E, param->confidence_mode, NULL);

    value = XTALK_CONFIG_CMD;
    g_ptrSpi.SpiReadBytes(&value, 1, &rValue, 1);

    while (1) {
        g_ptrSpi.rRegister(0x30, &value, NULL);

        timeOutCnt++;
        delay_1ms(1);
        if (timeOutCnt > 100) {
            g_ptrSysFlag.errorCode |= SET_16BIT(4);
            return -1;
        }

        if (value == 0x11) {
            g_ptrSysFlag.isGpioTrig0 = false;
            break;
        }
    }
    return 1;
}

//bin_num:  截断位置,即从0-bin_num之间选取xtalk bin
//x_bin:   x_bin = xtalk bin - ref bin的差值，real_bin = 实际主峰bin - ref bin的差值, |x_bin-real_bin|<5则认为是xtalk
//x_peak:   xtalk_bin的主峰,MA后
//flag:    1: > x_peak/2 有灰尘 2: > x_peak 非常脏

int8_t xtalk_calc(uint8_t bin_num, uint8_t *x_bin,uint16_t *x_peak)
{
    uint8_t value = 0, rValue = 0;
    uint8_t timeOutCnt = 0;

    Vi4302StopRanging();

    if(range_cnt_init(10) == -1) {
        return -1;
    }
    
	g_ptrSpi.wRegister( 0x31, bin_num, NULL);
    value = XTALK_CAL_CMD;
    g_ptrSpi.SpiReadBytes(&value, 1, &rValue, 1);

    value = 0;
    while (1) {
        g_ptrSpi.rRegister(0x30, &value, NULL);

        timeOutCnt++;
        delay_1ms(1);
        if (timeOutCnt > 1000) {
            g_ptrSysFlag.errorCode |= SET_16BIT(5);
            return -1;
        }

        if (value == 0x11) {
            g_ptrSpi.rRegister(0x31, &value, NULL);
            *x_bin = value;
            g_ptrSpi.rRegister(0x32, &value, NULL);
            *x_peak =  value;
            g_ptrSpi.rRegister(0x33, &value, NULL);
            *x_peak |=  value<<8;

            g_ptrSysFlag.isGpioTrig0 = false;
            break;
        }
    }	

	return 1;
} 


//修改出峰逻辑
//输入参数： conf_flg:0 优先输出TOF小的峰 1：优先输出置信度大的峰 
//返回值：0X11 成功 

int8_t search_logic_init(uint8_t conf_flg)
{
    uint8_t value = 0, rValue = 0;
    uint8_t timeOutCnt = 0;

    Vi4302StopRanging();
    
	g_ptrSpi.wRegister( 0x31, 0x00, NULL);
    g_ptrSpi.wRegister( 0x32, 0x04, NULL);
    g_ptrSpi.wRegister( 0x3E, conf_flg, NULL);
    value = XTALK_CONFIG_CMD;
    g_ptrSpi.SpiReadBytes(&value, 1, &rValue, 1);

    value = 0;
    while (1) {
        g_ptrSpi.rRegister(0x30, &value, NULL);

        timeOutCnt++;
        delay_1ms(1);
        if (timeOutCnt > 1000) {
            g_ptrSysFlag.errorCode |= SET_16BIT(5);
            return -1;
        }

        if (value == 0x11) {

            g_ptrSysFlag.isGpioTrig0 = false;
            break;
        }
    }	

	return 1;
}

int8_t range_cnt_init(uint8_t cnt)
{
    uint8_t value = 0, rValue = 0;
    uint8_t timeOutCnt = 0;
    uint16_t parameter_types = XTALK_RANG_CNT;
	g_ptrSpi.wRegister( 0x31, parameter_types&0xFF, NULL);
	g_ptrSpi.wRegister( 0x32, (parameter_types>>8)&0xFF, NULL);
	g_ptrSpi.wRegister( 0x3B, cnt, NULL);

    value = XTALK_CONFIG_CMD;
    g_ptrSpi.SpiReadBytes(&value, 1, &rValue, 1);

    while (1) {
        g_ptrSpi.rRegister(0x30, &value, NULL);

        timeOutCnt++;
        delay_1ms(1);
        if (timeOutCnt > 100) {
            g_ptrSysFlag.errorCode |= SET_16BIT(4);
            return -1;
        }

        if (value == 0x11) {
            g_ptrSysFlag.isGpioTrig0 = false;
            break;
        }
    }
    return 1;
}


void Vi4302RangingConfig(void) {
    int8_t status = -1;
    uint8_t s_value = 0;
    // uint8_t vbdStep = 0;
    // int8_t  vbdStatus = -1;
    // uint8_t timeOutCnt = 0;
    //_Bool  timeOutBreak = false;
    // uint8_t vbdTemp=0;
    // uint8_t tdcValue=0;
    // uint8_t vbdData[4] = {0};
    uint8_t m = 0;

    // enable vld
    VLD_ENABLE;

    // reset vi4302
    RESET_4302_LOW

    PROCESS_DELAY(5);

    // enable vi4302
    RESET_4302_HIGHT

    PROCESS_DELAY(50);

    // spi register test
    g_ptrSpi.rRegister(0x0014, &s_value, NULL);
    if ((s_value == 0xA0) || (s_value == 0xA1) || (s_value == 0xA2) ||
        (s_value == 0xA3) || (s_value == 0xA4)) {
        g_ptrSysFlag.errorCode &= CLEAR_16BIT(0);
    } else {
        g_ptrSysFlag.errorCode |= SET_16BIT(0);
    }

    // load firmware
    status = Vi4302McuFirmwareLoad();

    if (status == -1) {
        g_ptrSysFlag.errorCode |= SET_16BIT(6);
    }

    PROCESS_DELAY(100);

    //xtalk 
    xtalk_param_t xtalk_param;
    // g_ptrFlash.ReadFlashData((uint32_t)XTALK_ADDR, (uint8_t*)&xtalk_param.xtalk_bin, 8);

    
    if(g_param_combine.xtalk.xtalk_calc_cnt != 0 && g_param_combine.xtalk.xtalk_calc_cnt != 0xFFFF) {
        xtalk_param.parameter_types = MP_PARAMETER|XTALK_BIN|XTALK_PEAK|CONFIDENCE_MODE;
       
        xtalk_param.para_MP = 21;  
        xtalk_param.xtalk_bin = g_param_combine.xtalk.xtalk_bin;
        xtalk_param.xtalk_peak = g_param_combine.xtalk.xtalk_peak;
        xtalk_param.confidence_mode = 1;

        status = xtalk_init(&xtalk_param);
        if (status == -1) {
            g_ptrSysFlag.errorCode |= SET_16BIT(4);
        }

        /*status = search_logic_init(1);
        if (status == -1) {
            g_ptrSysFlag.errorCode |= SET_16BIT(4);
        }*/
    }

    // int register list
    status = 0;
    for (uint8_t m = 0; m < 3; m++) {
        status = Vi4302FaculaRegisterInit(kRanging);
        if (status == 1) {
            break;
        }
    }
    if (status != 1) {
        g_ptrSysFlag.errorCode |= SET_16BIT(7);
    }

    PROCESS_DELAY(100);
    // setting vbd voltage
    if ((g_ptrSysParam.vbdStep == 0x00 && g_ptrSysParam.vbdTmp == 0x00 &&
         g_ptrSysParam.tdcTmp == 0x00) ||
        (g_ptrSysParam.vbdStep == 0xFF && g_ptrSysParam.vbdTmp == 0xFF &&
         g_ptrSysParam.tdcTmp == 0xFF)) {
        /*g_ptrSysParam.vbdStep = 0;
        g_ptrSysParam.vbdTmp = 0;
        //auto calculate vbd
        status = vbdMultiCalc();
        tdcAutoCalculate(&tdcValue);

        if(status == 1) {
                vbdData[0] = g_ptrSysParam.vbdStep;
                vbdData[1] = g_ptrSysParam.vbdTmp;
                vbdData[2] = tdcValue;
                g_ptrFlash.WriteFlashSomeWord(VBD_ADDR,(uint8_t *)vbdData,4);
                g_ptrFlash.WriteFlashHalfWord(LIDAR_MODE_ADDR,kTofFaculaMode);//first
        toggle to greymap mode NVIC_SystemReset();
        }*/
        Vi4302ExeVbdTdc(1);

    } else {
        // setting vbd voltage
        g_ptrSpi.wRegister(0x0242, g_ptrSysParam.tdcTmp, NULL);
        g_ptrSpi.wRegister(0x024F, g_ptrSysParam.vbdStep, NULL);
        for (m = 0; m < 10; m++) {
            s_value = 0;
            g_ptrSpi.rRegister(0x024F, &s_value, NULL);
            if (s_value == g_ptrSysParam.vbdStep) {
                g_ptrSysFlag.errorCode &= CLEAR_16BIT(0);
                break;
            } else {
                g_ptrSpi.wRegister(0x0242, g_ptrSysParam.tdcTmp, NULL);
                g_ptrSpi.wRegister(0x024F, g_ptrSysParam.vbdStep, NULL);
                g_ptrSysFlag.errorCode |= SET_16BIT(1);
            }
        }

        g_ptrSpi.rRegister(0x0086, &s_value, NULL);
        g_ptrSysParam.integration = s_value<<8;
        g_ptrSpi.rRegister(0x0087, &s_value, NULL);
        g_ptrSysParam.integration |= s_value;

    }
    PROCESS_DELAY(10);
    g_ptrSysParam.currentTemperature = GettingTemperature(
        GettingAdcValue());  //((float)TEMPERATURE_TABLE[(GettingAdcValue()
                             //- 214)>>1])/10;
    g_ptrSysParam.temperatureLast = g_ptrSysParam.currentTemperature;
    g_ptrSysParam.currentTemperaturePri = g_ptrSysParam.currentTemperature;
}
