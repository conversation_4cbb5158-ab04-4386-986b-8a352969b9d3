# 红光dTOF测距雷达精度优化系统详解

> 相关文档: [[系统架构文档]]

## 1. 概述

本文档详细描述红光dTOF测距雷达系统的精度优化机制，包括多级校正算法、数据处理流程和优化策略。系统通过硬件校正、算法优化和软件滤波的多层次处理，实现±1cm的高精度测距。

## 2. 数据处理流程架构

### 2.1 整体数据流程图

```mermaid
flowchart TD
    A[VI4302原始数据] --> B[数据解析]
    B --> C[温度补偿校正]
    C --> D[噪声校正算法]
    D --> E[分段线性校正]
    E --> F[置信度评估]
    F --> G{置信度≥70%?}
    G -->|是| H[数据滤波处理]
    G -->|否| I[输出无效值-30000]
    H --> J[中值滤波]
    J --> K[平滑处理]
    K --> L[最终距离输出]
    
    subgraph "原始数据结构"
        M[TOF值 16bit]
        N[正常峰值 16bit]
        O[衰减峰值 16bit]
        P[正常噪声 24bit]
        Q[衰减噪声 24bit]
        R[积分次数 16bit]
        S[温度值]
    end
    
    A --> M
    A --> N
    A --> O
    A --> P
    A --> Q
    A --> R
    A --> S
```

### 2.2 核心处理函数调用流程

```mermaid
sequenceDiagram
    participant Main as 主程序
    participant Temp as 温度校正
    participant Noise as 噪声校正
    participant Comp as 分段校正
    participant Conf as 置信度评估
    participant Filter as 数据滤波
    
    Main->>Temp: Get_Temperature_Rectified_Tof()
    Temp-->>Main: 温度补偿后TOF
    
    Main->>Noise: Get_AttenNoise_Rectified_Tof()
    Noise-->>Main: 噪声校正后TOF和峰值
    
    Main->>Comp: vis_tof_compensation()
    Comp-->>Main: 分段校正后TOF
    
    Main->>Conf: Noise_To_Confidence()
    Conf-->>Main: 置信度值(0-100)
    
    alt 置信度≥70%
        Main->>Filter: VI4302_InfifoDataIn()
        Filter->>Filter: VI4302_InfifoDataOut()
        Filter-->>Main: 滤波后最终距离
    else 置信度<70%
        Main-->>Main: 输出-30000(无效)
    end
```

## 3. 多级精度校正算法详解

### 3.1 温度补偿校正算法

#### 3.1.1 算法原理
温度变化会影响激光器发射功率和接收器灵敏度，导致TOF测量偏差。系统采用二次多项式模型进行温度补偿：

**数学模型**:
```
Bias_ts = ((P1 × TS²) >> 14 + P2 × TS) >> 10
TOF_corrected = TOF_raw - Bias_ts
```

其中：
- `P1`, `P2`: 温度校正系数，存储在Flash中
- `TS`: 温度值 (°C × 16)
- `>>`: 右移位运算，用于定点数运算

#### 3.1.2 实现代码分析
```c
int16_t Get_Temperature_Rectified_Tof(int16_t TOFr, int32_t TS) {
    int16_t Bias_ts;
    // 二次多项式温度补偿计算
    Bias_ts = (int16_t)((((g_fmNeedSaved.wendu_cal_para[0] * TS * TS) >> 14) 
                        + g_fmNeedSaved.wendu_cal_para[1] * TS) >> 10);
    return TOFr - Bias_ts;
}
```

#### 3.1.3 参数标定方法
- **标定温度点**: -40°C, 0°C, +25°C, +85°C
- **标定目标**: 不同温度下的TOF偏差最小化
- **参数计算**: 最小二乘法拟合二次多项式系数

### 3.2 强度补偿与噪声校正算法

#### 3.2.1 强度补偿机制

系统具备完整的强度补偿功能，通过峰值强度校正确保不同反射率目标的测距精度：

**峰值强度预处理**:
```c
// 正常峰值强度补偿
P1r = ((temp_norm_peak - (uint16_t)(norm_noise * Al_Pa.MA_Coefficient / 64 / Al_Pa.PEAK_Coefficient)) << 6) / temp_intg;

// 衰减峰值处理
P2r = temp_atten_peak;
if (P2r > 10000) P2r = 0;  // 异常峰值过滤
```

**强度补偿原理**:
- **噪声基底去除**: 从原始峰值中减去噪声基底，获得真实信号强度
- **积分时间归一化**: 通过积分次数归一化，消除积分时间对强度的影响
- **增益补偿**: 通过PEAK_Coefficient调整信号增益，适应不同反射率目标
- **异常值过滤**: 自动检测和过滤异常强度值

#### 3.2.2 三级噪声处理架构

系统采用三级噪声处理机制，分别处理不同类型的噪声影响：

**NOISE1 - TOF偏移校正**:
```
bias_n = (A × atten_noise²) >> 18 + (B × atten_noise) >> 14 + C
TOF_corrected = TOF_raw - bias_n
```

**NOISE2 - 正常峰值校正**:
```
P1ans = (A × atten_noise³) >> 22 + (B × atten_noise²) >> 14 + (C × atten_noise) >> 6 + D
P1_corrected = (P1_raw × D) / P1ans
```

**NOISE3 - 衰减峰值校正**:
```
P2ans = (A × atten_noise²) >> 18 + (B × atten_noise) >> 8 + C
P2_corrected = (P2_raw × C) / P2ans
```

#### 3.2.2 噪声校正参数
```c
// NOISE1参数 - TOF偏移校正
#define NOISE1_PARAMETER_A    -261
#define NOISE1_PARAMETER_B    8181
#define NOISE1_PARAMETER_C    -3

// NOISE2参数 - 正常峰值校正
#define NOISE2_PARAMETER_A    -428
#define NOISE2_PARAMETER_B    669
#define NOISE2_PARAMETER_C    -403
#define NOISE2_PARAMETER_D    465

// NOISE3参数 - 衰减峰值校正
#define NOISE3_PARAMETER_A    2203
#define NOISE3_PARAMETER_B    -799
#define NOISE3_PARAMETER_C    381
```

#### 3.2.3 噪声校正原理详解

**重要澄清**: 三种噪声校正并非对应三种物理噪声源，而是针对同一噪声源（衰减噪声）对不同测量参数的影响进行分别校正。

**统一噪声源 - 衰减噪声(atten_noise)**:
- **来源**: VI4302传感器输出的24bit衰减噪声数据
- **物理意义**: 反映当前测量环境的综合噪声水平，包括光子噪声、探测器噪声、环境干扰等
- **作用**: 作为所有三级校正算法的输入参数，用于计算各种校正补偿值

**三级校正的处理对象**:

1. **NOISE1 - TOF时间校正**
   - **处理对象**: TOF测量值的系统偏移
   - **物理原理**: 噪声会影响时间测量的精度，导致TOF值产生系统性偏差
   - **数学模型**: 二次多项式 `bias_n = (A×noise²)>>18 + (B×noise)>>14 + C`

2. **NOISE2 - 正常峰值校正**
   - **处理对象**: 正常峰值(P1)的非线性响应校正
   - **物理原理**: 噪声水平影响峰值检测的准确性，需要对峰值进行归一化校正
   - **数学模型**: 三次多项式 `P1ans = (A×noise³)>>22 + (B×noise²)>>14 + (C×noise)>>6 + D`

3. **NOISE3 - 衰减峰值校正**
   - **处理对象**: 衰减峰值(P2)的非线性响应校正
   - **物理原理**: 衰减峰值在不同噪声环境下的响应特性不同，需要独立校正
   - **数学模型**: 二次多项式 `P2ans = (A×noise²)>>18 + (B×noise)>>8 + C`

**校正策略**:
- **统一输入**: 三种校正都使用同一个衰减噪声值作为输入
- **分别处理**: 针对TOF、P1峰值、P2峰值分别建立不同的数学模型
- **协同工作**: 三种校正共同作用，确保测距系统在各种噪声环境下的精度

### 3.3 分段线性校正算法

#### 3.3.1 两级全局校正

**第一级 - 正常峰值全局校正**:
```c
// 峰值范围限制
if (P1n < min_peak) P1ans = min_peak;
else if (P1n > max_peak) P1ans = max_peak;

// 全局偏移计算
Bias_g1 = ((para[0] × P1ans²) >> 24) + ((para[1] × P1ans) >> 14) + para[2];
TOF = TOF - Bias_g1;
```

**第二级 - 衰减峰值全局校正**:
```c
// 高反射率目标的额外校正
Bias_g2 = ((para[0] × P2ans²) >> 20) + ((para[1] × P2ans) >> 10) + para[2];
TOF = TOF - Bias_g2;
```

#### 3.3.2 分段线性插值校正

系统将测距范围分为多个段，每段使用不同的校正参数：

```c
// 查找TOF值所在的段
for (idx = 0; idx < segment_number; idx++) {
    if (TOF < segment_para[idx][2]) {  // 段边界
        pos = idx + 1;
        break;
    }
}

// 边界段处理
if (pos == 1 || pos == segment_number + 1) {
    // 使用单段参数
    offset = ((para[0] × peak) >> 12) + para[1];
} else {
    // 相邻段线性插值
    offset1 = ((para_prev[0] × peak) >> 12) + para_prev[1];
    offset2 = ((para_curr[0] × peak) >> 12) + para_curr[1];
    
    weightR = segment_boundary - TOF;
    weightL = TOF - prev_boundary;
    offset = (weightR × offset1 + weightL × offset2) / (weightR + weightL);
}
```

#### 3.3.3 分段校正原理
- **非线性补偿**: 不同距离段的系统非线性特性不同
- **线性插值**: 段边界处使用加权平均避免跳跃
- **参数存储**: 每段存储5个参数 [a1, a2, mt, min_peak, max_peak]

## 4. 置信度评估系统

### 4.1 置信度计算算法

#### 4.1.1 数学模型
```c
// 噪声阈值计算
noise_th = A/(noise + B) + ((C × noise) >> 16) + D
noise_th = MA_Coefficient × noise_th / 8

// 置信度区间计算
upper = MA_Coefficient × noise / 64 + 4 × noise_th
lower = MA_Coefficient × noise / 64 + 7 × noise_th / 5

// 置信度百分比
if (peak × 4 > upper) confidence = 100
else if (peak × 4 < lower) confidence = 0
else confidence = 100 × (peak × 4 - lower) / (upper - lower)
```

#### 4.1.2 置信度参数
```c
#define CONFIDENCE_PARAMETER_A    1374
#define CONFIDENCE_PARAMETER_B    350
#define CONFIDENCE_PARAMETER_C    1450
#define CONFIDENCE_PARAMETER_D    12
```

#### 4.1.3 算法原理
- **信噪比评估**: 基于峰值信号与噪声水平的比值
- **动态阈值**: 根据噪声水平动态调整置信度阈值
- **线性映射**: 将信噪比线性映射到0-100%置信度范围

### 4.2 数据有效性判断

#### 4.2.1 多重验证机制
```c
// 1. 置信度阈值检查
if (confidence < 70) {
    yuanshi_tof_distance = -30000;  // 无效数据标记
}

// 2. 距离范围检查
if (MuL_TOF.TOF0 == 65535) {
    // 传感器错误数据
}

// 3. 峰值有效性检查
if (P2r > 10000) {
    P2r = 0;  // 峰值异常处理
}
```

#### 4.2.2 无效数据处理策略
- **标记值**: -30000表示低置信度数据
- **错误码**: 65535表示传感器错误
- **数据过滤**: 自动过滤异常峰值和噪声数据

## 5. 数据滤波与平滑处理

### 5.1 中值滤波器

#### 5.1.1 三点中值滤波算法
```c
uint8_t Get_Mid_Position(uint16_t *p) {
    // 找出三个值中的中位数位置
    if (*p > *(p + 1)) {
        if (*(p + 1) > *(p + 2)) return 1;      // p[1]是中位数
        else if (*p > *(p + 2)) return 2;       // p[2]是中位数
        else return 0;                          // p[0]是中位数
    } else {
        if (*(p + 1) < *(p + 2)) return 1;      // p[1]是中位数
        else if (*p < *(p + 2)) return 2;       // p[2]是中位数
        else return 0;                          // p[0]是中位数
    }
}
```

#### 5.1.2 滤波器实现
```c
void Refresh_Tof_With_MidFilter(uint8_t *data) {
    uint16_t tof_value[3];
    uint8_t position;
    
    // 获取当前值和历史值
    tof_value[0] = data_temp_buf[0][0] | (data_temp_buf[0][1] << 8);  // 历史值1
    tof_value[1] = data_temp_buf[1][0] | (data_temp_buf[1][1] << 8);  // 历史值2
    tof_value[2] = (*data) | ((*(data + 1)) << 8);                   // 当前值
    
    // 选择中位数
    position = Get_Mid_Position(tof_value);
    if (position < 2) {
        // 使用历史数据作为输出
        for (i = 0; i < 16; i++) {
            *(data + i) = data_temp_buf[position][i];
        }
    }
    
    // 更新历史缓存
    for (i = 0; i < 16; i++) {
        data_temp_buf[0][i] = data_temp_buf[1][i];  // 历史值1 = 历史值2
        data_temp_buf[1][i] = curent_data_buf[i];   // 历史值2 = 当前值
    }
}
```

#### 5.1.3 中值滤波原理
- **突发噪声抑制**: 有效消除单点异常值
- **边缘保持**: 不会模糊真实的距离跳变
- **实时处理**: 仅需3点缓存，延迟最小

### 5.2 数据平滑处理

#### 5.2.1 滑动平均滤波
```c
// 数据输入到FIFO
VI4302_InfifoDataIn(yuanshi_tof_distance);

// 从FIFO输出平滑后的数据
g_ControlPara.vi4302_tof_data_no_tiaoling_offset = VI4302_InfifoDataOut();
```

#### 5.2.2 异常值检测
- **统计分析**: 基于历史数据统计特性检测异常
- **自适应阈值**: 根据数据变化动态调整检测阈值
- **渐进式滤波**: 对异常值进行渐进式处理而非直接丢弃

## 6. 精度优化模式

### 6.1 三种工作模式对比

| 模式 | 精度 | 更新频率 | 算法参数 | 适用场景 |
|------|------|----------|----------|----------|
| 高精度模式 | ±0.5cm | 10-20Hz | 高MA系数，多次积分 | 高精度测量 |
| 标准模式 | ±1cm | 50Hz | 标准参数 | 一般应用 |
| 高速模式 | ±2cm | 100Hz | 低MA系数，快速响应 | 实时跟踪 |

### 6.2 模式切换机制

#### 6.2.1 参数自适应调整
```c
typedef struct {
    uint16_t MA_Coefficient;    // 移动平均系数: 高精度(256) > 标准(128) > 高速(64)
    uint16_t PEAK_Coefficient;  // 峰值系数: 影响信号处理增益
    uint16_t LSB_Coefficient;   // LSB转换系数: 影响距离分辨率
    uint16_t Intg_Num;         // 积分次数: 高精度(16) > 标准(8) > 高速(4)
} Algorithm_Parameter;
```

#### 6.2.2 动态优化策略
- **环境自适应**: 根据噪声水平自动调整滤波强度
- **目标自适应**: 根据目标反射率调整算法参数
- **功耗平衡**: 在精度和功耗间找到最优平衡点

## 7. 校正参数管理

### 7.1 参数存储结构

#### 7.1.1 Flash存储布局
```c
typedef struct {
    int16_t wendu_cal_para[2];              // 温度校正参数 [P1, P2]
    int16_t heibai_quanju_para[5];          // 正常全局校正参数
    int16_t gaofan_quanju_para[4];          // 高反全局校正参数
    int16_t heibai_fenduan_para[15][5];     // 正常分段校正参数
    int16_t gaofan_fenduan_para[15][8];     // 高反分段校正参数
    int16_t point0_offset;                  // 零点偏移校正
    uint8_t heibai_fanduan_number;          // 分段数量
    uint8_t sensor_is_calibrated;           // 校正状态标志
} calibration_params_t;
```

#### 7.1.2 默认参数配置
```c
// 默认温度校正参数
#define DEFAULT_FLASH_TEMP_CAL_P1      510
#define DEFAULT_FLASH_TEMP_CAL_P2      143

// 默认全局校正参数
const int16_t VI4302_NORMAL_QUANJU_PARA[5] = {-25, -50, 1634, -147, 4817};
const int16_t VI4302_ATT_QUANJU_PARA[4] = {0, -2, 17, 12835};
```

### 7.2 参数更新机制

#### 7.2.1 实时参数更新
- **UART命令**: 支持通过串口命令实时更新校正参数
- **参数验证**: 更新前进行参数有效性检查
- **立即生效**: 参数更新后立即应用到算法中

#### 7.2.2 参数持久化
- **Flash写入**: 参数更新后自动写入Flash存储
- **掉电保持**: 确保校正参数在断电后不丢失
- **备份机制**: 关键参数支持多重备份

## 8. 性能指标与验证

### 8.1 精度性能指标
- **测距精度**: ±1cm (标准模式)
- **测距范围**: 0.1m - 10m
- **温度稳定性**: -40°C到+85°C范围内精度保持
- **响应时间**: <20ms (标准模式)

### 8.2 算法验证方法
- **标准目标测试**: 使用标准反射板进行精度验证
- **温度循环测试**: 全温度范围内的精度稳定性测试
- **长期稳定性测试**: 连续运行稳定性验证
- **多目标场景测试**: 复杂环境下的算法鲁棒性测试

---

> 相关文档: [[系统架构文档]]
