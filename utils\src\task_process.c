#include "task_process.h"

#include "communication.h"
#include "time_tasks.h"


/**
 * @brief
 *
 */
void task_process_init() {
    // SYS_time_slice_init();
}

/**
 * @brief 主调度循环
 *
 */
void main_loop(void) {
    for (;;) {
        g_time_task_ptr->task_poll();

        /* parse command */
        g_comm_ptr->comm_data_parse_loop();

        // 喂狗
        //    	fwdgt_counter_reload();

        //* periodically tasks
        // SYS_time_slice_process();

        //* prase cmd
        // package_deal();  //不放入1ms任务集，时间太长

        //* reset
        // if ((g_comm_flag.StFlag.reset_flag) && (g_protocol_ack_flag.ack_num == 0)) {
        //     g_comm_flag.StFlag.reset_flag = false;

        //     delay_1ms(1);
        //     // __set_FAULTMASK(1); //关闭所有中断
        //     // __disable_irq();
        //     NVIC_SystemReset();  // reset sys
        // }
    }
}

/**
 * @brief 待机模式下主调度循环
 *
 */
// void main_loop_pwr(void) {

//     // 唤醒定时器
//     TIM_Configuration();

//     for (;;) {
//         // 执行任务
//         tickless_process();
//     }
// }