/**
 * @file communication.h
 * <AUTHOR> name (<EMAIL>)
 * @brief 通用通讯模块，兼容UART、BLUETOOTH、TCP等等接口，包括通讯任务执行
 * @version 0.1
 * @date 2025-07-31
 *
 * @copyright Copyright (c) 2025
 *
 */
#ifndef _COMMUNICATION_H_
#define _COMMUNICATION_H_


#include "protocol.h"
#include "ring_buff.h"


#define RING_BUFF_REC_MAX_TIMES 10


typedef enum {
    eUART,
} ECommType;

enum {
    kWriteOk    = 0x01,
    kWriteError = 0x02,
    kReadOk     = 0x03,
};

/**
 * @brief 数据缓存buffer，发送和接收数据都可以缓存
 *
 */
typedef struct {
    StRingBuff  ring_buffer;
    StFrameInfo rec_buffe_size[RING_BUFF_REC_MAX_TIMES];
    uint8_t     rec_count;
} StCommFifo;

/**
 * @brief
 *
 */
typedef struct {
    uint8_t ackTimes;
    uint8_t ackType;
    uint8_t ackId;
} StParseAck;
extern StParseAck g_stParseAck;

/**
 * @brief 通讯接口
 *
 */
typedef struct {
    void (*comm_init)(ECommType , uint32_t);       // 接口函数
    void (*comm_data_parse_loop)(void);                       // 数据解析循环
    void (*comm_transmit_data)(uint8_t * = NULL, uint16_t buffSiz);  // 发送数据
} API_Communication_T;


extern UCommBuffer g_comm_buff;


extern const API_Communication_T *const g_comm_ptr;

#endif
