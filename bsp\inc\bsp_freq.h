/*
 * @Author: xx
 * @Date: 2024-05-21 20:06:29
 * @LastEditors: Do not edit
 * @LastEditTime: 2024-07-31 17:15:43
 * @Description:
 * @FilePath: \MDK-ARMd:\Project_data\05_project_code\local_SVN\vscode_workspace\fw01_dToF_lidar\Bsp\inc\bsp_freq.h
 */
#ifndef _BSP_FREQ_H
#define _BSP_FREQ_H

#include "variable_table.h"

void TrigModeTimer(uint16_t cout);
void motor_init(void);
// bool RegisterTrigModeCallBackFunc(PtrTrigTimer *ptr, void (*trigCallback)(void));

#endif
