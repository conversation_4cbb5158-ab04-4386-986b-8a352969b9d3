# 通讯和协议模块重构设计文档

## 1. 项目概述

本文档描述了对当前通讯和协议模块的重构方案，旨在提高代码的可维护性、可扩展性，并符合开闭原则和模块化设计原则。

## 2. 当前架构问题分析

### 2.1 主要问题
- **违反开闭原则**：添加新命令需要修改ParseProtocol函数中的switch语句
- **耦合度高**：通讯层和协议层混合在一起
- **可扩展性差**：只支持UART，添加其他接口需要大量修改
- **代码重复**：大量相似的命令处理逻辑
- **单一职责原则违反**：一个函数处理所有命令类型
- **硬编码严重**：命令ID和处理逻辑硬编码在switch中

### 2.2 现有代码结构
```
projects/app/src/communication.c  - 通讯接口和协议解析
utils/src/protocol.c              - 协议数据包构建
```

## 3. 重构架构设计

### 3.1 分层架构
```
┌─────────────────────────────────────────┐
│           Application Layer             │
│        (Business Logic)                 │
├─────────────────────────────────────────┤
│         Command Handler Layer           │
│    (Command Processing & Response)      │
├─────────────────────────────────────────┤
│          Protocol Layer                 │
│     (Encoding/Decoding & Validation)    │
├─────────────────────────────────────────┤
│        Communication Layer              │
│    (Buffer Management & Coordination)   │
├─────────────────────────────────────────┤
│          Transport Layer                │
│      (UART/TCP/Bluetooth/etc.)          │
└─────────────────────────────────────────┘
```

### 3.2 核心设计模式
- **策略模式**：不同传输接口的实现
- **命令模式**：命令处理的解耦
- **工厂模式**：命令处理器的创建
- **观察者模式**：事件通知机制

## 4. 接口设计

### 4.1 传输层接口
```c
typedef struct {
    int (*init)(void* config);
    int (*send)(const uint8_t* data, size_t len);
    int (*receive)(uint8_t* buffer, size_t max_len);
    int (*set_callback)(void (*callback)(uint8_t* data, size_t len));
    int (*deinit)(void);
    const char* name;
} transport_interface_t;
```

### 4.2 协议层接口
```c
typedef struct {
    int (*encode)(const void* cmd_data, uint8_t* buffer, size_t* len);
    int (*decode)(const uint8_t* buffer, size_t len, void* cmd_data);
    int (*validate)(const uint8_t* buffer, size_t len);
} protocol_interface_t;
```

### 4.3 命令处理器接口
```c
typedef struct {
    uint8_t cmd_id;
    int (*execute)(const void* cmd_data, void* response_data);
    const char* name;
} command_handler_t;
```

### 4.4 通讯管理器接口
```c
typedef struct {
    // 接口管理
    int (*register_transport)(const transport_interface_t* transport);
    int (*register_protocol)(const protocol_interface_t* protocol);
    int (*register_command_handler)(const command_handler_t* handler);
    
    // 运行时操作
    int (*send_command)(uint8_t cmd_id, const void* data, size_t len);
    int (*process_received_data)(void);
    int (*set_active_transport)(const char* transport_name);
    
    // 事件回调
    int (*set_event_callback)(void (*callback)(uint8_t event, void* data));
} communication_manager_t;
```

## 5. 文件结构设计

```
communication/
├── inc/
│   ├── comm_manager.h          // 通讯管理器接口
│   ├── transport_interface.h   // 传输层接口定义
│   ├── protocol_interface.h    // 协议层接口定义
│   ├── command_handler.h       // 命令处理器接口
│   ├── comm_types.h           // 通用类型定义
│   └── comm_api.h             // 对外API接口
├── src/
│   ├── comm_manager.c          // 通讯管理器实现
│   ├── transport/
│   │   ├── uart_transport.c    // UART传输实现
│   │   ├── tcp_transport.c     // TCP传输实现（预留）
│   │   └── bluetooth_transport.c // 蓝牙传输实现（预留）
│   ├── protocol/
│   │   ├── yapha_protocol.c    // 当前协议实现
│   │   └── custom_protocol.c   // 自定义协议实现（预留）
│   └── handlers/
│       ├── system_handlers.c   // 系统命令处理器
│       ├── lidar_handlers.c    // 激光雷达命令处理器
│       └── debug_handlers.c    // 调试命令处理器
```

## 6. 对外API设计

### 6.1 主要API接口
```c
typedef struct {
    // 初始化和配置
    int (*init)(void);
    int (*config_transport)(const char* transport_name, void* config);
    
    // 数据收发
    int (*send_data)(uint8_t cmd_id, const void* data, size_t len);
    int (*register_receive_callback)(uint8_t cmd_id, void (*callback)(const void* data, size_t len));
    
    // 状态查询
    int (*get_connection_status)(void);
    int (*get_last_error)(void);
    
    // 高级功能
    int (*send_async)(uint8_t cmd_id, const void* data, size_t len, void (*callback)(int result));
    int (*set_timeout)(uint32_t timeout_ms);
} comm_api_t;

// 全局API实例
extern const comm_api_t* g_comm_api;
```

### 6.2 使用示例
```c
// 初始化
g_comm_api->init();
g_comm_api->config_transport("uart", &uart_config);

// 发送命令
uint16_t mode = kTofScanMode;
g_comm_api->send_data(kTofMode, &mode, sizeof(mode));

// 注册接收回调
g_comm_api->register_receive_callback(kTemperature, temperature_handler);
```

## 7. 错误处理和日志系统

### 7.1 错误码定义
```c
typedef enum {
    COMM_OK = 0,
    COMM_ERROR_INVALID_PARAM,
    COMM_ERROR_NOT_INITIALIZED,
    COMM_ERROR_TRANSPORT_FAILED,
    COMM_ERROR_PROTOCOL_ERROR,
    COMM_ERROR_TIMEOUT,
    COMM_ERROR_BUFFER_FULL,
    COMM_ERROR_HANDLER_NOT_FOUND,
    COMM_ERROR_MAX
} comm_error_t;
```

### 7.2 日志系统
```c
#define COMM_LOG(level, fmt, ...) \
    comm_log(level, __FILE__, __LINE__, fmt, ##__VA_ARGS__)
```

## 8. 实施计划

### 8.1 实施阶段
1. **第一阶段**：设计和实现核心接口定义
2. **第二阶段**：实现通讯管理器核心功能
3. **第三阶段**：迁移现有UART和协议实现
4. **第四阶段**：重构命令处理逻辑
5. **第五阶段**：添加错误处理和日志系统
6. **第六阶段**：测试和优化

### 8.2 向后兼容策略
- 保留原有的全局变量和函数名
- 提供适配层，让现有代码无缝迁移
- 逐步替换旧的实现

## 9. 测试策略
- 单元测试每个接口实现
- 集成测试整个通讯流程
- 压力测试验证性能和稳定性

## 10. 预期收益
- 提高代码可维护性和可读性
- 支持多种传输接口的快速扩展
- 降低模块间耦合度
- 便于单元测试和调试
- 为未来功能扩展奠定基础
