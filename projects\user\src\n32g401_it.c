/**
*     Copyright (c) 2022, Nations Technologies Inc.
* 
*     All rights reserved.
*
*     This software is the exclusive property of Nations Technologies Inc. (Hereinafter 
* referred to as NATIONS). This software, and the product of NATIONS described herein 
* (Hereinafter referred to as the Product) are owned by NATIONS under the laws and treaties
* of the People's Republic of China and other applicable jurisdictions worldwide.
*
*     NATIONS does not grant any license under its patents, copyrights, trademarks, or other 
* intellectual property rights. Names and brands of third party may be mentioned or referred 
* thereto (if any) for identification purposes only.
*
*     NATIONS reserves the right to make changes, corrections, enhancements, modifications, and 
* improvements to this software at any time without notice. Please contact NATIONS and obtain 
* the latest version of this software before placing orders.

*     Although NATIONS has attempted to provide accurate and reliable information, NATIONS assumes 
* no responsibility for the accuracy and reliability of this software.
* 
*     It is the responsibility of the user of this software to properly design, program, and test 
* the functionality and safety of any application made of this information and any resulting product. 
* In no event shall NATIONS be liable for any direct, indirect, incidental, special,exemplary, or 
* consequential damages arising in any way out of the use of this software or the Product.
*
*     NATIONS Products are neither intended nor warranted for usage in systems or equipment, any
* malfunction or failure of which may cause loss of human life, bodily injury or severe property 
* damage. Such applications are deemed, "Insecure Usage".
*
*     All Insecure Usage shall be made at user's risk. User shall indemnify NATIONS and hold NATIONS 
* harmless from and against all claims, costs, damages, and other liabilities, arising from or related 
* to any customer's Insecure Usage.

*     Any express or implied warranty with regard to this software or the Product, including,but not 
* limited to, the warranties of merchantability, fitness for a particular purpose and non-infringement
* are disclaimed to the fullest extent permitted by law.

*     Unless otherwise explicitly permitted by NATIONS, anyone may not duplicate, modify, transcribe
* or otherwise distribute this software for any purposes, in whole or in part.
*
*     NATIONS products and technologies shall not be used for or incorporated into any products or systems
* whose manufacture, use, or sale is prohibited under any applicable domestic or foreign laws or regulations. 
* User shall comply with any applicable export control laws and regulations promulgated and administered by 
* the governments of any countries asserting jurisdiction over the parties or transactions.
**/

/**
*\*\file n32g401_it.c
*\*\author Nations
*\*\version v1.0.0
*\*\copyright Copyright (c) 2022, Nations Technologies Inc. All rights reserved.
**/
#include "n32g401_it.h"
#include "n32g401.h"

/*** Cortex-M4 Processor Exceptions Handlers ***/

/**
*\*\name    NMI_Handler.
*\*\fun     This function handles NMI exception.
*\*\param   none
*\*\return  none 
**/
void NMI_Handler(void)
{
    /* This interrupt is generated when HSE clock fails */

    if (RCC_Interrupt_Status_Get(RCC_INT_CLKSSIF) != RESET)
    {
        /* At this stage: HSE, PLL are disabled (but no change on PLL config) and HSI
            is selected as system clock source */

        /* Enable HSE */
        RCC_HSE_Config(RCC_HSE_ENABLE);

        /* Enable HSE Ready interrupt */
        RCC_Interrupt_Enable(RCC_INT_HSERDIEN);

#ifndef SYSCLK_HSE
        /* Enable PLL Ready interrupt */
        RCC_Interrupt_Enable(RCC_INT_PLLRDIEN);
#endif /* SYSCLK_HSE */

        /* Clear Clock Security System interrupt pending bit */
        RCC_Interrupt_Status_Clear(RCC_INT_CLKSSICLR);

        /* Once HSE clock recover, the HSERDY interrupt is generated and in the RCC INTSTS
           routine the system clock will be reconfigured to its previous state (before
           HSE clock failure) */
    }
}

/**
*\*\name    HardFault_Handler.
*\*\fun     This function handles Hard Fault exception.
*\*\param   none
*\*\return  none 
**/
void HardFault_Handler(void)
{
    /* Go to infinite loop when Hard Fault exception occurs */
    while (1)
    {
    }
}

/**
*\*\name    MemManage_Handler.
*\*\fun     This function handles Memory Manage exception.
*\*\param   none
*\*\return  none 
**/
void MemManage_Handler(void)
{
    /* Go to infinite loop when Memory Manage exception occurs */
    while (1)
    {
    }
}

/**
*\*\name    BusFault_Handler.
*\*\fun     This function handles Bus Fault exception.
*\*\param   none
*\*\return  none 
**/
void BusFault_Handler(void)
{
    /* Go to infinite loop when Bus Fault exception occurs */
    while (1)
    {
    }
}

/**
*\*\name    UsageFault_Handler.
*\*\fun     This function handles Usage Fault exception.
*\*\param   none
*\*\return  none 
**/
void UsageFault_Handler(void)
{
    /* Go to infinite loop when Usage Fault exception occurs */
    while (1)
    {
    }
}

/**
*\*\name    SVC_Handler.
*\*\fun     This function handles SVCall exception.
*\*\param   none
*\*\return  none 
**/
void SVC_Handler(void)
{
}

/**
*\*\name    DebugMon_Handler.
*\*\fun     This function handles Debug Monitor exception.
*\*\param   none
*\*\return  none 
**/
void DebugMon_Handler(void)
{
}

/**
*\*\name    SysTick_Handler.
*\*\fun     This function handles SysTick Handler.
*\*\param   none
*\*\return  none 
**/
void SysTick_Handler(void)
{
}

/** N32G401 Peripherals Interrupt Handlers, interrupt handler's name please refer 
     to the startup file (startup_n32g401.s) **/

/** This function handles RCC interrupt request. **/
/**
*\*\name    RCC_IRQHandler.
*\*\fun     This function handles RCC interrupt request.
*\*\param   none
*\*\return  none 
**/
void RCC_IRQHandler(void)
{
    if (RCC_Interrupt_Status_Get(RCC_INT_HSERDIF) != RESET)
    {
        /* Clear HSERDY interrupt pending bit */
        RCC_Interrupt_Status_Clear(RCC_INT_HSERDICLR);

        /* Check if the HSE clock is still available */
        if (RCC_Flag_Status_Get(RCC_FLAG_HSERD) != RESET)
        {
#ifdef SYSCLK_HSE
            /* Select HSE as system clock source */
            RCC_Sysclk_Config(RCC_SYSCLK_SRC_HSE);
#else
            /* Enable PLL: once the PLL is ready the PLLRDY interrupt is generated */
            RCC_PLL_Enable();
#endif /* SYSCLK_HSE */
        }
    }

    if (RCC_Interrupt_Status_Get(RCC_INT_PLLRDIF) != RESET)
    {
        /* Clear PLLRDY interrupt pending bit */
        RCC_Interrupt_Status_Clear(RCC_INT_PLLRDICLR);

        /* Check if the PLL is still locked */
        if (RCC_Flag_Status_Get(RCC_FLAG_PLLRD) != RESET)
        {
            /* Select PLL as system clock source */
            RCC_Sysclk_Config(RCC_SYSCLK_SRC_PLLCLK);
        }
    }
}


