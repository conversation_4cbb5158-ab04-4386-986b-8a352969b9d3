#include "speed_control.h"

PtrRegSpeed g_regSpeed;

/***cal angle****/
void EncoderCalcEdgeCallbackFunc(uint16_t count) {
    if (g_ptrLidarStatus.lidarMode == kTofScanMode || g_ptrLidarStatus.lidarMode == kTofCalibrationMode) {
        CLOSE_LED1
    }
    g_regEncoder.calcEdgeCountAve = count;  //(count + g_regEncoder.calcEdgeCountLast)>>1;
    // g_regEncoder.calcEdgeCountLast = count;
    g_regEncoder.calc_delta_t2      = count;
    g_regEncoder.calc_delta_t2_duty = g_regEncoder.calc_delta_t2 * 100 / (g_regEncoder.calc_delta_t2 + g_regEncoder.mark_delta_t1);

    // printf("2:%d %d\r\n",g_regEncoder.calc_delta_t2, g_regEncoder.calc_delta_t2_duty);
    if (g_regSpeed.motorOK < 10 || g_regSpeed.isDetectStartEncoder == false) {
        g_regSpeed.motorOK++;
    } else {
        g_regSpeed.isStalled = false;
        if (g_ptrLidarStatus.lidarMode == kTofScanMode || g_ptrLidarStatus.lidarMode == kTofCalibrationMode) {
            VLD_ENABLE
        }
    }

    if (g_regSpeed.isDetectStartEncoder == true) {
        g_regSpeed.isDetectStartEncoder = false;
        g_regEncoder.calcEdgeNum        = 0;
        if (g_cal_data_t.is_ignore_one_circle == false) {
            g_regEncoder.startPack = 1;  // ��ʼ֡�ź� ��ʼ���
        } else {
            g_cal_data_t.is_ignore_one_circle = false;
        }
    } else {
        g_regEncoder.calcEdgeNum++;  // ��n�����̳�

        if (g_regEncoder.calcEdgeNum >= 15) {  // ����Ϊ15ʱ
            g_regEncoder.calcEdgeNum = 0;
        }
    }

    g_pid_speed.speed_interval_cnt++;
    g_regEncoder.calcEdgeSum += count;  // g_regEncoder.calcEdgeCountAve;
}

/***mark 0��****/
void EncoderMarkEdgeCallbackFunc(uint16_t count) {
    float spd = 0;
    if (g_ptrLidarStatus.lidarMode == kTofScanMode || g_ptrLidarStatus.lidarMode == kTofCalibrationMode) {
        OPEN_LED1
    }

    if (!g_regSpeed.isSpeedStabled) {
#if !defined(A0_T5_XX) && !defined(A2_T5_XX)
        if (g_regEncoder.markEdgeCount < ENCODER_NUM) {
            g_regEncoder.markEdgeCountSum += count;
            g_regEncoder.markEdgeCount++;  // ���ڴֵ����ۼ����̳���
        } else {
            g_regEncoder.markEdgeCount = 0;
            if (g_regEncoder.markEdgeCountSum > SLOW_COUNT) {
                g_regEncoder.markEdgeQuickCount  = 0;
                g_regEncoder.markEdgeStableCount = 0;
                if (g_regEncoder.markEdgeSlowCount < 1) {
                    g_regEncoder.markEdgeSlowCount++;
                } else {
                    g_regEncoder.markEdgeSlowCount = 0;
                    g_ptrFlagCmd.cmdHandle0        = true;  // �·�������Ϣ��־(FF FF)
                }
            } else if (g_regEncoder.markEdgeCountSum < QUICK_COUNT) {
                g_regEncoder.markEdgeSlowCount   = 0;
                g_regEncoder.markEdgeStableCount = 0;
                if (g_regEncoder.markEdgeQuickCount < 1) {
                    g_regEncoder.markEdgeQuickCount++;
                } else {
                    g_regEncoder.markEdgeQuickCount = 0;
                    g_ptrFlagCmd.cmdHandle1         = true;  // �·�������Ϣ��־��FE FE)
                }
            } else {
                g_regEncoder.markEdgeQuickCount = 0;
                g_regEncoder.markEdgeSlowCount  = 0;
                if (g_regEncoder.markEdgeStableCount < 2) {
                    g_regEncoder.markEdgeStableCount++;  // ת���ȶ�����
                } else {
                    g_regSpeed.isSpeedStabled = true;
                    g_regEncoder.calcEdgeSum  = 0;
                    g_ptrFlagCmd.cmdHandle2   = true;  // ת���ȶ��·�FA FA��־
                }
            }
            g_regEncoder.markEdgeCountSum = 0;
        }
#else
        g_regSpeed.isSpeedStabled = true;
        g_regSpeed.isSendStabled  = true;
#endif


    } else {
        g_regEncoder.mark_delta_t1      = count;
        g_regEncoder.mark_delta_t1_duty = g_regEncoder.mark_delta_t1 * 100 / (g_regEncoder.mark_delta_t1 + g_regEncoder.calc_delta_t2);

// printf("1:%d %d\r\n",g_regEncoder.mark_delta_t1, g_regEncoder.mark_delta_t1_duty);
#if (defined A0_D6_XX) || (defined A2_D6_XX)
#if USING_COIN_WHOLE_ENCODER == 1
        if (g_regEncoder.mark_delta_t1_duty_last >= 52 && g_regEncoder.calc_delta_t2_duty >= 48 && g_regEncoder.mark_delta_t1_duty <= 48) {
            g_regSpeed.isDetectStartEncoder = true;  // ���ִ�С��

            spd                     = (1000000.0 / (g_regEncoder.calcEdgeSum));
            g_regSpeed.currentSpeed = (uint16_t)((spd + 0.03f) * 10);

            g_regEncoder.calcEdgeSum = 0;
            // printf("ok\r\n");
        }
#else
        if (g_regEncoder.mark_delta_t1_duty_last <= 47 && g_regEncoder.calc_delta_t2_duty >= 52 && g_regEncoder.mark_delta_t1_duty >= 51) {
            g_regSpeed.isDetectStartEncoder = true;  // ���ִ�С��

            spd                     = (1000000.0 / (g_regEncoder.calcEdgeSum));
            g_regSpeed.currentSpeed = (uint16_t)((spd + 0.03f) * 10);

            g_regEncoder.calcEdgeSum = 0;
            // printf("ok\r\n");
        }
#endif
#else
        if (g_regEncoder.mark_delta_t1_duty_last <= 47 && g_regEncoder.calc_delta_t2_duty >= 52 && g_regEncoder.mark_delta_t1_duty >= 51) {
            g_regSpeed.isDetectStartEncoder = true;  // ���ִ�С��

            spd                     = (1000000.0 / (g_regEncoder.calcEdgeSum));
            g_regSpeed.currentSpeed = (uint16_t)((spd + 0.03f) * 10);

            g_regEncoder.calcEdgeSum = 0;
        }
#endif

        g_regEncoder.mark_delta_t1_duty_last = g_regEncoder.mark_delta_t1_duty;
    }

    g_ptrDDSInfoCount.encoder_cnt = getAbsolutionTime();
}