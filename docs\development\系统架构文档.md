# 红光dTOF测距雷达系统架构文档

## 1. 项目概述

### 1.1 项目简介
本项目是一个基于VI4302芯片的红光dTOF（直接飞行时间）测距雷达系统，主要用于水下测距应用。系统采用N32G401微控制器作为主控芯片，通过SPI接口与VI4302传感器芯片通信。

### 1.2 技术规格
- **主控芯片**: N32G401 (ARM Cortex-M4, 72MHz)
- **传感器芯片**: VI4302 (dTOF激光测距芯片)
- **通信接口**: UART2 (460800 bps)
- **工作电压**: 3.3V
- **测距范围**: 水下环境优化

## 2. 系统工作模式

系统支持两种主要工作模式：

### 2.1 单像素模式 (_SINGLE_PIXEL_MODE = 0x55)
- **功能**: 单点测距模式，输出25个像素点的峰值数据
- **用途**: 用于调试和单点精确测距
- **数据输出**: 25个16位峰值数据 (50字节)
- **触发命令**: USER_MP_INFO_CMD, VI430X_PIXEL_GET_CMD

### 2.2 测距模式 (_RANG_MODE = 0xAA)
- **功能**: 连续测距模式，实时输出距离数据
- **用途**: 正常工作模式，提供连续的距离测量
- **数据输出**: 包含距离、温度、置信度等信息的23字节数据包
- **触发命令**: USER_RANGING_CMD, VI430X_RANGING_SET_CMD

## 3. 校正系统

系统包含多种校正模式以确保测量精度：

### 3.1 传感器校正 (Sensor Calibration)
- **TDC校正**: 时间数字转换器校正
- **BVD校正**: 基准电压校正，支持温度补偿
- **温度校正**: 基于NTC温度传感器的温度补偿

### 3.2 串扰校正 (Xtalk Calibration)
- **功能**: 消除光学串扰影响
- **实现**: 通过xtalk_cal()函数执行标定
- **存储**: 校正参数存储在Flash中

### 3.3 系统参数校正
- **置信度参数**: confidence_k_init()配置置信度阈值
- **灰尘检测**: Dust_detection_init()配置灰尘检测参数
- **算法参数**: 支持MA系数、峰值系数、LSB系数等配置

## 4. 精度校正与优化系统

> 详细内容请参考: [[精度优化系统详解]]

系统具备完整的精度校正和优化机制，通过多级算法处理确保±1cm的测距精度：

### 4.1 核心校正算法
- **温度补偿校正**: 二次多项式温度补偿，全温度范围精度保持
- **噪声校正算法**: 三级噪声处理（NOISE1/2/3），消除各类噪声影响
- **分段线性校正**: 两级全局校正+分段插值，实现高精度距离补偿
- **置信度评估**: 基于信噪比的数据质量评估，70%阈值过滤

### 4.2 数据处理流程
- **数据滤波**: 中值滤波消除突发噪声，滑动平均平滑数据
- **异常检测**: 自动识别和处理异常测距数据
- **参数自适应**: 根据环境条件动态调整算法参数

### 4.3 精度优化模式
- **高精度模式**: ±0.5cm精度，10-20Hz更新频率
- **标准模式**: ±1cm精度，50Hz更新频率
- **高速模式**: ±2cm精度，100Hz更新频率

## 5. 系统架构图

```mermaid
graph TB
    A[主控制器 N32G401] --> B[VI4302传感器芯片]
    A --> C[UART2通信接口]
    A --> D[Flash存储]
    A --> E[ADC温度检测]
    A --> F[GPIO控制]
    
    B --> G[激光发射器]
    B --> H[光电接收器]
    B --> I[SPI接口]
    
    C --> J[上位机/外部设备]
    D --> K[校正参数存储]
    D --> L[系统配置存储]
    
    E --> M[NTC温度传感器]
    F --> N[状态指示LED]
    F --> O[控制信号]
    
    subgraph "工作模式"
        P[单像素模式<br/>0x55]
        Q[测距模式<br/>0xAA]
    end
    
    subgraph "校正系统"
        R[TDC校正]
        S[BVD校正]
        T[串扰校正]
        U[温度补偿]
    end
```

## 6. 软件架构

### 6.1 主要模块结构

```mermaid
graph LR
    A[main.c] --> B[系统初始化]
    A --> C[主循环]
    
    B --> D[时钟配置]
    B --> E[外设初始化]
    B --> F[VI4302初始化]
    
    C --> G[UART处理]
    C --> H[指令执行]
    
    G --> I[协议解析]
    H --> J[模式切换]
    H --> K[数据处理]
    
    subgraph "BSP层"
        L[GPIO驱动]
        M[UART驱动]
        N[SPI驱动]
        O[ADC驱动]
        P[Flash驱动]
    end
    
    subgraph "应用层"
        Q[命令处理]
        R[数据转换]
        S[校正算法]
        T[工作模式管理]
    end
```

### 6.2 核心数据结构

#### 系统配置结构体
```c
typedef struct {
    uint8_t work_mode;              // 工作模式 (_SINGLE_PIXEL_MODE 或 _RANG_MODE)
    uint8_t sensor_is_calibrated;   // 传感器是否已校正
    uint8_t sensor_cal_tdc;         // TDC校正值
    uint8_t sensor_cal_bvd;         // BVD校正值
    int8_t  sensor_cal_ts;          // 校正时的温度
    uint8_t xtalk_is_calibrated;    // 串扰是否已校正
    uint8_t xtalk_is_write;         // 串扰参数是否已写入
    A2_Configurable_Parameters sys_A2_Conf_Para;
} system_config_t;
```

#### BVD校正结构体
```c
typedef struct {
    int8_t  Cur_Temp;    // 当前温度
    int8_t  OTP_Temp;    // OTP存储温度
    uint8_t OTP_BVD;     // OTP存储BVD值
} BVD_CAL;
```

## 7. 通信协议

### 7.1 协议格式
- **帧头**: 0x5A
- **操作码**: 1字节命令码
- **数据长度**: 2字节 (小端序)
- **数据载荷**: 变长数据
- **校验和**: 1字节

### 6.2 主要命令
- **USER_MP_INFO_CMD**: 获取单像素数据
- **USER_RANGING_CMD**: 开始/停止测距
- **USER_REG_R_CMD**: 读取寄存器
- **USER_REG_W_CMD**: 写入寄存器
- **USER_HIST_R_CMD**: 读取直方图数据

## 7. 数据流程

### 7.1 测距数据流程
1. VI4302传感器采集原始数据
2. 通过SPI接口读取16字节原始数据
3. 进行温度补偿和校正计算
4. 计算置信度和最终距离值
5. 通过UART发送23字节数据包

### 7.2 校正流程
1. 系统上电后检查校正状态
2. 如未校正，执行相应校正程序
3. 校正参数存储到Flash
4. 后续测量使用校正参数进行补偿

## 8. 关键特性

### 8.1 温度补偿
- 实时NTC温度检测
- 基于温度的BVD动态调整
- 温度相关的算法参数补偿

### 8.2 置信度评估
- 基于噪声水平的置信度计算
- 置信度阈值可配置
- 低置信度数据自动过滤

### 8.3 灰尘检测
- 检测光学窗口污染
- 可配置检测参数
- 提供污染程度标识

### 8.4 数据滤波
- 内置数据滤波算法
- 异常值检测和处理
- 平滑输出数据

## 9. 存储管理

### 9.1 Flash分区
- **系统配置区**: 存储工作模式和校正状态
- **校正参数区**: 存储各种校正系数
- **用户数据区**: 存储用户配置参数

### 9.2 参数持久化
- 校正参数自动保存
- 系统配置掉电保持
- 支持恢复出厂设置

## 10. 错误处理

### 10.1 硬件错误
- SPI通信错误检测
- 传感器状态监控
- 看门狗保护机制

### 10.2 数据错误
- 校验和验证
- 数据范围检查
- 异常数据过滤

## 11. 性能指标

### 11.1 测距性能
- **精度**: ±1cm (典型值)
- **测距范围**: 0.1m - 10m (水下)
- **更新频率**: 可配置，最高100Hz

### 11.2 系统性能
- **启动时间**: <100ms
- **响应时间**: <10ms
- **功耗**: <500mW (典型值)

---

*文档版本: v1.0*  
*创建日期: 2024年*  
*最后更新: 2024年*
